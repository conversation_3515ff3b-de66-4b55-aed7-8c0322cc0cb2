# 感恩日記應用 - 部署維護指南

## 概述

本指南提供感恩日記應用的完整部署流程、監控策略、維護計劃和故障排除方案，確保應用在生產環境中的穩定運行。

## 部署環境規劃

### 1. 環境分層
```
開發環境 (Development)
├── 本地開發
├── Firebase Emulators
└── 測試數據

測試環境 (Staging)
├── Firebase Staging Project
├── 模擬生產數據
└── 自動化測試

生產環境 (Production)
├── Firebase Production Project
├── 真實用戶數據
└── 監控告警
```

### 2. Firebase專案配置
```bash
# 創建多環境Firebase專案
firebase projects:create gratitude-diary-dev
firebase projects:create gratitude-diary-staging
firebase projects:create gratitude-diary-prod

# 配置專案別名
firebase use --add gratitude-diary-dev --alias dev
firebase use --add gratitude-diary-staging --alias staging
firebase use --add gratitude-diary-prod --alias prod
```

## CI/CD 流水線

### 1. GitHub Actions配置
```yaml
# .github/workflows/deploy.yml
name: Deploy Gratitude Diary

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test
      
      - name: Run linting
        run: npm run lint
      
      - name: Build application
        run: npm run build

  deploy-staging:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build for staging
        run: npm run build:staging
        env:
          REACT_APP_ENV: staging
          REACT_APP_FIREBASE_API_KEY: ${{ secrets.STAGING_FIREBASE_API_KEY }}
          REACT_APP_FIREBASE_PROJECT_ID: ${{ secrets.STAGING_FIREBASE_PROJECT_ID }}
      
      - name: Deploy to Firebase Staging
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_STAGING }}'
          projectId: gratitude-diary-staging
          channelId: live

  deploy-production:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build for production
        run: npm run build:production
        env:
          REACT_APP_ENV: production
          REACT_APP_FIREBASE_API_KEY: ${{ secrets.PROD_FIREBASE_API_KEY }}
          REACT_APP_FIREBASE_PROJECT_ID: ${{ secrets.PROD_FIREBASE_PROJECT_ID }}
      
      - name: Deploy to Firebase Production
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_PROD }}'
          projectId: gratitude-diary-prod
          channelId: live
```

### 2. 部署腳本
```bash
#!/bin/bash
# scripts/deploy.sh

set -e

ENVIRONMENT=${1:-staging}
echo "部署到 $ENVIRONMENT 環境..."

# 檢查環境參數
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    echo "錯誤: 環境參數必須是 'staging' 或 'production'"
    exit 1
fi

# 安裝依賴
echo "安裝依賴..."
npm ci

# 運行測試
echo "運行測試..."
npm test -- --coverage --watchAll=false

# 建置應用
echo "建置應用..."
if [ "$ENVIRONMENT" = "production" ]; then
    npm run build:production
else
    npm run build:staging
fi

# 部署到Firebase
echo "部署到Firebase..."
firebase use $ENVIRONMENT
firebase deploy --only hosting,functions,firestore,storage

echo "部署完成！"
echo "應用URL: https://gratitude-diary-$ENVIRONMENT.web.app"
```

## 監控與告警

### 1. Firebase Performance Monitoring
```javascript
// src/utils/performance.js
import { getPerformance, trace } from 'firebase/performance';

const perf = getPerformance();

export const performanceMonitor = {
  // 頁面載入性能
  trackPageLoad: (pageName) => {
    const pageTrace = trace(perf, `page_load_${pageName}`);
    pageTrace.start();
    
    window.addEventListener('load', () => {
      pageTrace.stop();
    });
  },

  // API請求性能
  trackApiCall: async (apiName, apiCall) => {
    const apiTrace = trace(perf, `api_${apiName}`);
    apiTrace.start();
    
    try {
      const result = await apiCall();
      apiTrace.putAttribute('status', 'success');
      return result;
    } catch (error) {
      apiTrace.putAttribute('status', 'error');
      apiTrace.putAttribute('error_code', error.code || 'unknown');
      throw error;
    } finally {
      apiTrace.stop();
    }
  },

  // 自定義指標
  trackCustomMetric: (metricName, value) => {
    const customTrace = trace(perf, metricName);
    customTrace.start();
    customTrace.putMetric('value', value);
    customTrace.stop();
  }
};
```

### 2. 錯誤監控
```javascript
// src/utils/errorTracking.js
import { getFunctions, httpsCallable } from 'firebase/functions';

const functions = getFunctions();
const logError = httpsCallable(functions, 'logError');

export class ErrorTracker {
  static async trackError(error, context = {}) {
    const errorData = {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: context.userId,
      context
    };

    try {
      await logError(errorData);
    } catch (logError) {
      console.error('Failed to log error:', logError);
    }
  }

  static setupGlobalErrorHandling() {
    // 捕獲未處理的Promise拒絕
    window.addEventListener('unhandledrejection', (event) => {
      this.trackError(event.reason, { type: 'unhandled_promise_rejection' });
    });

    // 捕獲全局錯誤
    window.addEventListener('error', (event) => {
      this.trackError(event.error, { 
        type: 'global_error',
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      });
    });
  }
}
```

### 3. 用戶行為分析
```javascript
// src/utils/analytics.js
import { getAnalytics, logEvent } from 'firebase/analytics';

const analytics = getAnalytics();

export const analyticsTracker = {
  // 頁面瀏覽
  trackPageView: (pageName) => {
    logEvent(analytics, 'page_view', {
      page_title: pageName,
      page_location: window.location.href
    });
  },

  // 用戶操作
  trackUserAction: (action, parameters = {}) => {
    logEvent(analytics, action, {
      timestamp: Date.now(),
      ...parameters
    });
  },

  // 日記相關事件
  trackDiaryEvent: (eventType, entryData) => {
    logEvent(analytics, `diary_${eventType}`, {
      emotion: entryData.emotion,
      has_media: entryData.media?.length > 0,
      word_count: entryData.content?.length || 0,
      tag_count: entryData.tags?.length || 0
    });
  },

  // 專注工作事件
  trackFocusEvent: (eventType, sessionData) => {
    logEvent(analytics, `focus_${eventType}`, {
      work_duration: sessionData.workDuration,
      break_duration: sessionData.breakDuration,
      completed: sessionData.isCompleted
    });
  }
};
```

## 數據備份策略

### 1. 自動備份配置
```javascript
// functions/src/backup/scheduler.js
const functions = require('firebase-functions');
const { firestore } = require('firebase-admin');

// 每日備份
exports.dailyBackup = functions.pubsub
  .schedule('0 2 * * *')
  .timeZone('Asia/Taipei')
  .onRun(async (context) => {
    const client = new firestore.v1.FirestoreAdminClient();
    const projectId = process.env.GCLOUD_PROJECT;
    const databaseName = client.databasePath(projectId, '(default)');
    
    const bucket = `gs://${projectId}-backup`;
    const timestamp = new Date().toISOString().split('T')[0];
    
    try {
      const [operation] = await client.exportDocuments({
        name: databaseName,
        outputUriPrefix: `${bucket}/daily/${timestamp}`,
        collectionIds: ['users', 'entries', 'interactions', 'focusSessions']
      });
      
      console.log(`每日備份已啟動: ${operation.name}`);
      
      // 發送備份成功通知
      await sendBackupNotification('success', {
        type: 'daily',
        timestamp,
        operation: operation.name
      });
      
    } catch (error) {
      console.error('每日備份失敗:', error);
      
      // 發送備份失敗告警
      await sendBackupNotification('error', {
        type: 'daily',
        timestamp,
        error: error.message
      });
    }
  });

// 週備份
exports.weeklyBackup = functions.pubsub
  .schedule('0 3 * * 0')
  .timeZone('Asia/Taipei')
  .onRun(async (context) => {
    // 週備份邏輯
  });

// 備份清理
exports.cleanupOldBackups = functions.pubsub
  .schedule('0 4 * * 0')
  .timeZone('Asia/Taipei')
  .onRun(async (context) => {
    // 清理30天前的備份
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 30);
    
    // 實施清理邏輯
  });
```

### 2. 數據恢復程序
```bash
#!/bin/bash
# scripts/restore-backup.sh

BACKUP_DATE=${1}
ENVIRONMENT=${2:-staging}

if [ -z "$BACKUP_DATE" ]; then
    echo "使用方法: $0 <BACKUP_DATE> [ENVIRONMENT]"
    echo "範例: $0 2024-01-15 staging"
    exit 1
fi

echo "從 $BACKUP_DATE 恢復數據到 $ENVIRONMENT 環境..."

# 設置Firebase專案
firebase use $ENVIRONMENT

# 執行恢復
gcloud firestore import gs://gratitude-diary-$ENVIRONMENT-backup/daily/$BACKUP_DATE \
    --project=gratitude-diary-$ENVIRONMENT

echo "數據恢復完成"
```

## 性能優化

### 1. 數據庫優化
```javascript
// 複合索引優化
const indexes = [
  {
    collectionGroup: 'entries',
    fields: [
      { fieldPath: 'userId', order: 'ASCENDING' },
      { fieldPath: 'privacy', order: 'ASCENDING' },
      { fieldPath: 'createdAt', order: 'DESCENDING' }
    ]
  },
  {
    collectionGroup: 'entries',
    fields: [
      { fieldPath: 'emotion', order: 'ASCENDING' },
      { fieldPath: 'createdAt', order: 'DESCENDING' }
    ]
  }
];

// 查詢優化
const optimizedQueries = {
  getUserEntries: (userId, limit = 20) => {
    return db.collection('entries')
      .where('userId', '==', userId)
      .orderBy('createdAt', 'desc')
      .limit(limit);
  },
  
  getPublicEntries: (emotion, limit = 20) => {
    let query = db.collection('entries')
      .where('privacy', '==', 'public');
    
    if (emotion) {
      query = query.where('emotion', '==', emotion);
    }
    
    return query.orderBy('createdAt', 'desc').limit(limit);
  }
};
```

### 2. 緩存策略
```javascript
// src/utils/cache.js
class CacheManager {
  constructor() {
    this.cache = new Map();
    this.ttl = new Map();
  }

  set(key, value, ttlMs = 5 * 60 * 1000) {
    this.cache.set(key, value);
    this.ttl.set(key, Date.now() + ttlMs);
  }

  get(key) {
    if (this.isExpired(key)) {
      this.delete(key);
      return null;
    }
    return this.cache.get(key);
  }

  isExpired(key) {
    const expiry = this.ttl.get(key);
    return expiry && Date.now() > expiry;
  }

  delete(key) {
    this.cache.delete(key);
    this.ttl.delete(key);
  }

  clear() {
    this.cache.clear();
    this.ttl.clear();
  }
}

export const cacheManager = new CacheManager();
```

## 安全性維護

### 1. 安全規則審計
```bash
#!/bin/bash
# scripts/security-audit.sh

echo "執行安全性審計..."

# 檢查Firestore規則
firebase firestore:rules:get > current-firestore-rules.txt
echo "Firestore規則已導出到 current-firestore-rules.txt"

# 檢查Storage規則
firebase storage:rules:get > current-storage-rules.txt
echo "Storage規則已導出到 current-storage-rules.txt"

# 檢查Functions配置
firebase functions:config:get > current-functions-config.json
echo "Functions配置已導出到 current-functions-config.json"

echo "安全性審計完成"
```

### 2. 定期安全更新
```javascript
// 依賴安全檢查
const securityCheck = {
  // 檢查過期的依賴
  checkOutdatedDependencies: async () => {
    const { execSync } = require('child_process');
    try {
      const result = execSync('npm audit --json', { encoding: 'utf8' });
      const audit = JSON.parse(result);
      return audit;
    } catch (error) {
      console.error('安全檢查失敗:', error);
      return null;
    }
  },

  // 更新安全補丁
  updateSecurityPatches: async () => {
    const { execSync } = require('child_process');
    try {
      execSync('npm audit fix', { stdio: 'inherit' });
      console.log('安全補丁更新完成');
    } catch (error) {
      console.error('安全補丁更新失敗:', error);
    }
  }
};
```

## 故障排除

### 1. 常見問題診斷
```javascript
// src/utils/diagnostics.js
export const diagnostics = {
  // 網路連接檢查
  checkNetworkConnection: async () => {
    try {
      const response = await fetch('/api/health', { 
        method: 'HEAD',
        cache: 'no-cache'
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  },

  // Firebase服務狀態檢查
  checkFirebaseServices: async () => {
    const services = {
      auth: false,
      firestore: false,
      storage: false,
      functions: false
    };

    try {
      // 檢查Authentication
      const user = firebase.auth().currentUser;
      services.auth = user !== null;

      // 檢查Firestore
      await firebase.firestore().doc('health/check').get();
      services.firestore = true;

      // 檢查Storage
      await firebase.storage().ref('health/check.txt').getDownloadURL();
      services.storage = true;

      // 檢查Functions
      const healthCheck = firebase.functions().httpsCallable('healthCheck');
      await healthCheck();
      services.functions = true;

    } catch (error) {
      console.error('Firebase服務檢查失敗:', error);
    }

    return services;
  },

  // 性能診斷
  performanceCheck: () => {
    const timing = performance.timing;
    return {
      pageLoadTime: timing.loadEventEnd - timing.navigationStart,
      domReadyTime: timing.domContentLoadedEventEnd - timing.navigationStart,
      firstPaintTime: performance.getEntriesByType('paint')[0]?.startTime || 0
    };
  }
};
```

### 2. 自動恢復機制
```javascript
// src/utils/recovery.js
export class AutoRecovery {
  static async handleApiError(error, retryCount = 0) {
    const maxRetries = 3;
    const retryDelay = Math.pow(2, retryCount) * 1000; // 指數退避

    if (retryCount >= maxRetries) {
      throw error;
    }

    // 特定錯誤的恢復策略
    switch (error.code) {
      case 'NETWORK_ERROR':
        await this.waitForNetworkRecovery();
        break;
      case 'AUTH_TOKEN_EXPIRED':
        await this.refreshAuthToken();
        break;
      case 'RATE_LIMIT_EXCEEDED':
        await this.delay(retryDelay);
        break;
      default:
        await this.delay(retryDelay);
    }

    return retryCount + 1;
  }

  static async waitForNetworkRecovery() {
    return new Promise((resolve) => {
      const checkConnection = () => {
        if (navigator.onLine) {
          resolve();
        } else {
          setTimeout(checkConnection, 1000);
        }
      };
      checkConnection();
    });
  }

  static async refreshAuthToken() {
    const user = firebase.auth().currentUser;
    if (user) {
      await user.getIdToken(true);
    }
  }

  static delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

這個部署維護指南提供了完整的生產環境管理方案，確保感恩日記應用能夠穩定、安全、高效地為用戶提供服務。
