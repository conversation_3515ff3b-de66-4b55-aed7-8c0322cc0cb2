"use client";
import "./chunk-C6WWHQR7.js";
import {
  createSvgIcon
} from "./chunk-QVI57TQV.js";
import "./chunk-YZ6PAUYO.js";
import "./chunk-BRYCWNNY.js";
import "./chunk-6YD57FQ6.js";
import {
  require_jsx_runtime
} from "./chunk-3NBMPMSA.js";
import {
  __toESM
} from "./chunk-DAFPDBRK.js";

// node_modules/@mui/icons-material/esm/Email.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var Email_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2m0 4-8 5-8-5V6l8 5 8-5z"
}), "Email");
export {
  Email_default as default
};
//# sourceMappingURL=@mui_icons-material_Email.js.map
