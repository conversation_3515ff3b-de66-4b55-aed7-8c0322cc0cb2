import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  AppBar,
  Toolbar,
  IconButton,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Switch,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Chip,
  Snackbar,
  Alert,
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import NotificationsIcon from '@mui/icons-material/Notifications';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import CenterFocusStrongIcon from '@mui/icons-material/CenterFocusStrong';
import FavoriteIcon from '@mui/icons-material/Favorite';
import { motion } from 'framer-motion';

const NotificationSettingsPage = () => {
  const navigate = useNavigate();
  const [settings, setSettings] = useState({
    dailyReminder: true,
    dailyReminderTime: '20:00',
    focusReminder: true,
    focusBreakReminder: true,
    socialNotifications: true,
    weeklyReport: true,
    pushNotifications: true,
  });
  const [showTimeDialog, setShowTimeDialog] = useState(false);
  const [selectedTimeType, setSelectedTimeType] = useState('');
  const [tempTime, setTempTime] = useState('');
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  const handleBack = () => {
    navigate('/app/settings');
  };

  const handleSettingChange = (key) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const handleTimeChange = (timeType) => {
    setSelectedTimeType(timeType);
    setTempTime(settings[timeType] || '20:00');
    setShowTimeDialog(true);
  };

  const handleTimeConfirm = () => {
    setSettings(prev => ({
      ...prev,
      [selectedTimeType]: tempTime,
    }));
    setShowTimeDialog(false);
    setShowSuccessMessage(true);
  };

  const notificationItems = [
    {
      icon: <NotificationsIcon />,
      title: '每日感恩提醒',
      subtitle: '提醒您記錄今天的感恩事項',
      key: 'dailyReminder',
      hasTime: true,
      timeKey: 'dailyReminderTime',
    },
    {
      icon: <CenterFocusStrongIcon />,
      title: '專注工作提醒',
      subtitle: '開始專注工作時的提醒',
      key: 'focusReminder',
    },
    {
      icon: <AccessTimeIcon />,
      title: '休息時間提醒',
      subtitle: '專注工作結束後的休息提醒',
      key: 'focusBreakReminder',
    },
    {
      icon: <FavoriteIcon />,
      title: '社群互動通知',
      subtitle: '點讚、評論和分享的通知',
      key: 'socialNotifications',
    },
  ];

  const additionalSettings = [
    {
      title: '週報總結',
      subtitle: '每週感恩回顧和統計報告',
      key: 'weeklyReport',
    },
    {
      title: '推播通知',
      subtitle: '允許應用發送推播通知',
      key: 'pushNotifications',
    },
  ];

  return (
    <Box sx={{ 
      bgcolor: 'background.default', 
      minHeight: '100vh',
      width: '100%',
      maxWidth: '100vw',
      margin: '0 auto',
    }}>
      {/* 頂部導航欄 */}
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar sx={{ maxWidth: '600px', width: '100%', margin: '0 auto' }}>
          <IconButton onClick={handleBack} sx={{ mr: 2 }}>
            <ArrowBackIcon sx={{ color: 'text.primary' }} />
          </IconButton>
          <Typography
            variant="h3"
            sx={{
              flex: 1,
              color: 'text.primary',
              fontWeight: 600,
            }}
          >
            通知設定
          </Typography>
        </Toolbar>
      </AppBar>

      <Box sx={{ 
        p: 2,
        maxWidth: '600px',
        width: '100%',
        margin: '0 auto',
      }}>
        {/* 主要通知設定 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            提醒設定
          </Typography>
          <Card sx={{ mb: 3, borderRadius: 4 }}>
            <List sx={{ py: 0 }}>
              {notificationItems.map((item, index) => (
                <ListItem
                  key={item.key}
                  sx={{
                    py: 2,
                    borderBottom: index < notificationItems.length - 1 ? '1px solid' : 'none',
                    borderColor: 'divider',
                  }}
                >
                  <ListItemIcon sx={{ color: 'primary.main' }}>
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {item.title}
                        </Typography>
                        {item.hasTime && settings[item.key] && (
                          <Chip
                            label={settings[item.timeKey]}
                            size="small"
                            onClick={() => handleTimeChange(item.timeKey)}
                            sx={{
                              bgcolor: 'primary.light',
                              color: 'primary.main',
                              cursor: 'pointer',
                              '&:hover': {
                                bgcolor: 'primary.main',
                                color: 'white',
                              },
                            }}
                          />
                        )}
                      </Box>
                    }
                    secondary={
                      <Typography variant="body2" color="text.secondary">
                        {item.subtitle}
                      </Typography>
                    }
                  />
                  <Switch
                    checked={settings[item.key]}
                    onChange={() => handleSettingChange(item.key)}
                    color="primary"
                  />
                </ListItem>
              ))}
            </List>
          </Card>
        </motion.div>

        {/* 其他設定 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            其他設定
          </Typography>
          <Card sx={{ mb: 3, borderRadius: 4 }}>
            <List sx={{ py: 0 }}>
              {additionalSettings.map((item, index) => (
                <ListItem
                  key={item.key}
                  sx={{
                    py: 2,
                    borderBottom: index < additionalSettings.length - 1 ? '1px solid' : 'none',
                    borderColor: 'divider',
                  }}
                >
                  <ListItemText
                    primary={
                      <Typography variant="body1" sx={{ fontWeight: 500 }}>
                        {item.title}
                      </Typography>
                    }
                    secondary={
                      <Typography variant="body2" color="text.secondary">
                        {item.subtitle}
                      </Typography>
                    }
                  />
                  <Switch
                    checked={settings[item.key]}
                    onChange={() => handleSettingChange(item.key)}
                    color="primary"
                  />
                </ListItem>
              ))}
            </List>
          </Card>
        </motion.div>

        {/* 通知權限說明 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card sx={{ borderRadius: 4, bgcolor: 'primary.light', color: 'primary.main' }}>
            <CardContent>
              <Typography variant="body2" sx={{ textAlign: 'center' }}>
                💡 提示：部分通知功能需要瀏覽器權限，請在瀏覽器設定中允許通知。
              </Typography>
            </CardContent>
          </Card>
        </motion.div>
      </Box>

      {/* 時間設定對話框 */}
      <Dialog
        open={showTimeDialog}
        onClose={() => setShowTimeDialog(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>設定提醒時間</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            type="time"
            value={tempTime}
            onChange={(e) => setTempTime(e.target.value)}
            sx={{ mt: 2 }}
            InputLabelProps={{
              shrink: true,
            }}
          />
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            選擇您希望收到每日感恩提醒的時間
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowTimeDialog(false)}>
            取消
          </Button>
          <Button onClick={handleTimeConfirm} variant="contained">
            確認
          </Button>
        </DialogActions>
      </Dialog>

      {/* 成功提示 */}
      <Snackbar
        open={showSuccessMessage}
        autoHideDuration={3000}
        onClose={() => setShowSuccessMessage(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert 
          onClose={() => setShowSuccessMessage(false)} 
          severity="success"
          sx={{ width: '100%' }}
        >
          通知設定已更新！
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default NotificationSettingsPage;
