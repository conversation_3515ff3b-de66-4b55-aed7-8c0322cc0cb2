# 感恩日記 - 混合架構API設計文件

## 架構概述

本專案採用混合架構設計：
- **後端API**: PHP + MySQL (自建)
- **推播通知**: Firebase Cloud Messaging
- **前端**: React Web應用
- **多平台**: 統一API支援Web、iOS、Android

## 技術架構

```
┌─────────────────────────────────────────────────────────┐
│                    客戶端應用層                           │
├─────────────┬─────────────┬─────────────┬─────────────────┤
│   Web App   │  iOS App    │ Android App │  Desktop App    │
│  (React)    │  (Swift)    │  (Kotlin)   │  (Electron)     │
└─────────────┴─────────────┴─────────────┴─────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                  自建PHP API                             │
│              (Laravel/CodeIgniter)                      │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────┬─────────────────────────┬─────────────────┐
│   MySQL     │   Firebase FCM          │   File Storage  │
│  Database   │   (推播通知)             │   (本地/雲端)    │
└─────────────┴─────────────────────────┴─────────────────┘
```

## 數據庫設計 (MySQL)

### 1. 用戶表 (users)
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    uid VARCHAR(255) UNIQUE NOT NULL COMMENT 'Firebase UID或自定義ID',
    email VARCHAR(255) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    avatar VARCHAR(500) NULL,
    bio TEXT NULL,
    language VARCHAR(10) DEFAULT 'zh-TW',
    timezone VARCHAR(50) DEFAULT 'Asia/Taipei',
    theme VARCHAR(20) DEFAULT 'light',
    
    -- 隱私設定
    profile_visible BOOLEAN DEFAULT TRUE,
    allow_comments BOOLEAN DEFAULT TRUE,
    allow_messages BOOLEAN DEFAULT FALSE,
    
    -- 通知設定
    daily_reminder BOOLEAN DEFAULT TRUE,
    focus_reminder BOOLEAN DEFAULT TRUE,
    social_interaction BOOLEAN DEFAULT TRUE,
    push_enabled BOOLEAN DEFAULT TRUE,
    
    -- 統計數據
    total_entries INT DEFAULT 0,
    total_likes INT DEFAULT 0,
    total_comments INT DEFAULT 0,
    focus_sessions INT DEFAULT 0,
    focus_minutes INT DEFAULT 0,
    streak_days INT DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_uid (uid),
    INDEX idx_created_at (created_at)
);
```

### 2. 日記條目表 (entries)
```sql
CREATE TABLE entries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content TEXT NOT NULL,
    emotion VARCHAR(50) NOT NULL,
    tags JSON NULL COMMENT '標籤數組',
    media JSON NULL COMMENT '媒體文件信息',
    privacy ENUM('public', 'friends', 'private') DEFAULT 'public',
    is_anonymous BOOLEAN DEFAULT FALSE,
    
    -- 位置信息
    latitude DECIMAL(10, 8) NULL,
    longitude DECIMAL(11, 8) NULL,
    address VARCHAR(500) NULL,
    
    -- 互動統計
    likes_count INT DEFAULT 0,
    comments_count INT DEFAULT 0,
    shares_count INT DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_emotion (emotion),
    INDEX idx_privacy (privacy),
    INDEX idx_created_at (created_at),
    INDEX idx_user_created (user_id, created_at),
    FULLTEXT idx_content (content)
);
```

### 3. 互動記錄表 (interactions)
```sql
CREATE TABLE interactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    entry_id INT NOT NULL,
    type ENUM('like', 'comment', 'share') NOT NULL,
    content TEXT NULL COMMENT '評論內容',
    parent_id INT NULL COMMENT '回覆的評論ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (entry_id) REFERENCES entries(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES interactions(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_like (user_id, entry_id, type),
    INDEX idx_entry_type (entry_id, type),
    INDEX idx_user_type (user_id, type),
    INDEX idx_created_at (created_at)
);
```

### 4. 專注工作記錄表 (focus_sessions)
```sql
CREATE TABLE focus_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    work_duration INT NOT NULL COMMENT '工作時長(分鐘)',
    break_duration INT NOT NULL COMMENT '休息時長(分鐘)',
    actual_work_time INT NULL COMMENT '實際工作時間(分鐘)',
    gratitude_message TEXT NULL,
    is_completed BOOLEAN DEFAULT FALSE,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_start_time (start_time),
    INDEX idx_is_completed (is_completed)
);
```

### 5. FCM Token表 (fcm_tokens)
```sql
CREATE TABLE fcm_tokens (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    token VARCHAR(500) NOT NULL,
    platform ENUM('web', 'ios', 'android', 'desktop') NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_token (user_id, token),
    INDEX idx_user_active (user_id, is_active),
    INDEX idx_platform (platform)
);
```

### 6. 通知記錄表 (notifications)
```sql
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    data JSON NULL COMMENT '額外數據',
    is_read BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP NULL,
    read_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_read (user_id, is_read),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at)
);
```

## API端點設計

### 基礎配置
- **Base URL**: `https://your-domain.com/api/v1`
- **認證方式**: Bearer Token (JWT)
- **內容類型**: `application/json`

### 1. 認證API

#### 1.1 用戶註冊
```http
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "display_name": "張小明",
  "fcm_token": "fcm_token_here",
  "platform": "web"
}

Response:
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "uid": "user_uid",
      "email": "<EMAIL>",
      "display_name": "張小明",
      "token": "jwt_token"
    }
  },
  "message": "註冊成功"
}
```

#### 1.2 用戶登入
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "fcm_token": "fcm_token_here",
  "platform": "web"
}
```

### 2. 日記管理API

#### 2.1 創建日記
```http
POST /entries
Authorization: Bearer {token}
Content-Type: application/json

{
  "content": "今天很感恩...",
  "emotion": "gratitude",
  "tags": ["工作", "家庭"],
  "privacy": "public",
  "is_anonymous": false,
  "latitude": 25.0330,
  "longitude": 121.5654,
  "address": "台北市信義區"
}
```

#### 2.2 獲取日記列表
```http
GET /entries?page=1&limit=20&emotion=gratitude&privacy=public&user_id=1
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "entries": [
      {
        "id": 1,
        "user_id": 1,
        "content": "今天很感恩...",
        "emotion": "gratitude",
        "tags": ["工作", "家庭"],
        "privacy": "public",
        "likes_count": 5,
        "comments_count": 2,
        "user": {
          "id": 1,
          "display_name": "張小明",
          "avatar": "avatar_url"
        },
        "created_at": "2024-01-01 10:00:00"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "total_pages": 5
    }
  }
}
```

### 3. 社群互動API

#### 3.1 點讚/取消點讚
```http
POST /entries/{entry_id}/like
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "liked": true,
    "likes_count": 6
  }
}
```

#### 3.2 添加評論
```http
POST /entries/{entry_id}/comments
Authorization: Bearer {token}
Content-Type: application/json

{
  "content": "很棒的分享！",
  "parent_id": null
}
```

### 4. 專注工作API

#### 4.1 開始專注工作
```http
POST /focus/start
Authorization: Bearer {token}
Content-Type: application/json

{
  "work_duration": 25,
  "break_duration": 10,
  "gratitude_message": "感謝今天的工作機會"
}
```

#### 4.2 完成專注工作
```http
POST /focus/{session_id}/complete
Authorization: Bearer {token}
Content-Type: application/json

{
  "actual_work_time": 25,
  "is_completed": true
}
```

### 5. 推播通知API

#### 5.1 更新FCM Token
```http
POST /notifications/token
Authorization: Bearer {token}
Content-Type: application/json

{
  "fcm_token": "new_fcm_token",
  "platform": "web"
}
```

#### 5.2 發送通知
```http
POST /notifications/send
Authorization: Bearer {token}
Content-Type: application/json

{
  "user_ids": [1, 2, 3],
  "title": "新的點讚",
  "content": "有人點讚了你的日記",
  "type": "like",
  "data": {
    "entry_id": 123,
    "from_user_id": 456
  }
}
```

## PHP實作架構

### 1. 目錄結構 (Laravel風格)
```
api/
├── app/
│   ├── Controllers/
│   │   ├── AuthController.php
│   │   ├── EntryController.php
│   │   ├── UserController.php
│   │   ├── FocusController.php
│   │   └── NotificationController.php
│   ├── Models/
│   │   ├── User.php
│   │   ├── Entry.php
│   │   ├── Interaction.php
│   │   ├── FocusSession.php
│   │   └── Notification.php
│   ├── Services/
│   │   ├── AuthService.php
│   │   ├── FCMService.php
│   │   └── FileUploadService.php
│   └── Middleware/
│       ├── AuthMiddleware.php
│       └── CorsMiddleware.php
├── config/
│   ├── database.php
│   ├── firebase.php
│   └── app.php
├── routes/
│   └── api.php
└── public/
    └── index.php
```

### 2. 核心配置文件
```php
// config/database.php
<?php
return [
    'host' => $_ENV['DB_HOST'] ?? 'localhost',
    'database' => $_ENV['DB_DATABASE'] ?? 'gratitude_diary',
    'username' => $_ENV['DB_USERNAME'] ?? 'root',
    'password' => $_ENV['DB_PASSWORD'] ?? '',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'options' => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]
];
```

```php
// config/firebase.php
<?php
return [
    'project_id' => $_ENV['FIREBASE_PROJECT_ID'],
    'server_key' => $_ENV['FCM_SERVER_KEY'],
    'sender_id' => $_ENV['FCM_SENDER_ID'],
    'api_key' => $_ENV['FIREBASE_API_KEY'],
];
```

這個混合架構設計提供了：
1. ✅ **成本控制**: 自建API降低運營成本
2. ✅ **數據掌控**: MySQL數據完全自主
3. ✅ **推播可靠**: Firebase FCM穩定可靠
4. ✅ **擴展靈活**: PHP生態系統豐富
5. ✅ **部署簡單**: 傳統LAMP架構易於部署
