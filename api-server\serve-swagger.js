const http = require('http');
const fs = require('fs');
const path = require('path');

const server = http.createServer((req, res) => {
  // 設置 CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  if (req.url === '/swagger.json') {
    try {
      const swaggerContent = fs.readFileSync(path.join(__dirname, 'swagger.json'), 'utf8');
      res.setHeader('Content-Type', 'application/json');
      res.writeHead(200);
      res.end(swaggerContent);
    } catch (error) {
      res.writeHead(404);
      res.end('Swagger file not found');
    }
  } else {
    res.writeHead(404);
    res.end('Not found');
  }
});

const PORT = 8080;
server.listen(PORT, () => {
  console.log(`Swagger server running on http://localhost:${PORT}`);
  console.log(`Swagger JSON available at http://localhost:${PORT}/swagger.json`);
});
