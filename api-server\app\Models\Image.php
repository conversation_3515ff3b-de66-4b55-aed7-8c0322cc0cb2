<?php

namespace App\Models;

use App\Services\Database;

/**
 * 圖片模型
 * 處理日記相關的圖片上傳和管理
 */
class Image extends BaseModel
{
    protected $table = 'images';
    
    protected $fillable = [
        'filename',
        'stored_filename', 
        'file_path',
        'file_url',
        'file_size',
        'mime_type',
        'width',
        'height',
        'entry_id',
        'user_id'
    ];

    /**
     * 創建圖片記錄
     */
    public function create($data)
    {
        $sql = "INSERT INTO {$this->table} (
            filename, stored_filename, file_path, file_url, 
            file_size, mime_type, width, height, entry_id, user_id, 
            created_at, updated_at
        ) VALUES (
            :filename, :stored_filename, :file_path, :file_url,
            :file_size, :mime_type, :width, :height, :entry_id, :user_id,
            NOW(), NOW()
        )";

        $params = [
            ':filename' => $data['filename'],
            ':stored_filename' => $data['stored_filename'],
            ':file_path' => $data['file_path'],
            ':file_url' => $data['file_url'],
            ':file_size' => $data['file_size'],
            ':mime_type' => $data['mime_type'],
            ':width' => $data['width'] ?? null,
            ':height' => $data['height'] ?? null,
            ':entry_id' => $data['entry_id'] ?? null,
            ':user_id' => $data['user_id']
        ];

        $this->db->execute($sql, $params);
        $imageId = $this->db->getLastInsertId();
        
        return $this->findById($imageId);
    }

    /**
     * 根據日記 ID 獲取圖片
     */
    public function getByEntryId($entryId)
    {
        $sql = "SELECT * FROM {$this->table} WHERE entry_id = :entry_id ORDER BY created_at ASC";
        return $this->db->fetchAll($sql, [':entry_id' => $entryId]);
    }

    /**
     * 根據用戶 ID 獲取圖片
     */
    public function getByUserId($userId, $limit = 50, $offset = 0)
    {
        $sql = "SELECT * FROM {$this->table} 
                WHERE user_id = :user_id 
                ORDER BY created_at DESC 
                LIMIT :limit OFFSET :offset";
        
        return $this->db->fetchAll($sql, [
            ':user_id' => $userId,
            ':limit' => $limit,
            ':offset' => $offset
        ]);
    }

    /**
     * 刪除圖片記錄
     */
    public function delete($id)
    {
        // 先獲取圖片信息以便刪除文件
        $image = $this->findById($id);
        if (!$image) {
            return false;
        }

        // 刪除數據庫記錄
        $sql = "DELETE FROM {$this->table} WHERE id = :id";
        $result = $this->db->execute($sql, [':id' => $id]);

        // 刪除物理文件
        if ($result && file_exists($image['file_path'])) {
            unlink($image['file_path']);
        }

        return $result;
    }

    /**
     * 更新圖片關聯的日記 ID
     */
    public function updateEntryId($imageId, $entryId)
    {
        $sql = "UPDATE {$this->table} SET entry_id = :entry_id, updated_at = NOW() WHERE id = :id";
        return $this->db->execute($sql, [
            ':entry_id' => $entryId,
            ':id' => $imageId
        ]);
    }

    /**
     * 獲取圖片統計信息
     */
    public function getStats($userId)
    {
        $sql = "SELECT 
                    COUNT(*) as total_images,
                    SUM(file_size) as total_size,
                    COUNT(CASE WHEN entry_id IS NOT NULL THEN 1 END) as attached_images,
                    COUNT(CASE WHEN entry_id IS NULL THEN 1 END) as unattached_images
                FROM {$this->table} 
                WHERE user_id = :user_id";
        
        return $this->db->fetchOne($sql, [':user_id' => $userId]);
    }

    /**
     * 清理未關聯的圖片（超過24小時）
     */
    public function cleanupUnattachedImages()
    {
        $sql = "SELECT * FROM {$this->table} 
                WHERE entry_id IS NULL 
                AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)";
        
        $unattachedImages = $this->db->fetchAll($sql);
        
        foreach ($unattachedImages as $image) {
            $this->delete($image['id']);
        }
        
        return count($unattachedImages);
    }

    /**
     * 驗證圖片文件
     */
    public static function validateImage($file)
    {
        $errors = [];
        
        // 檢查文件是否上傳成功
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $errors[] = '文件上傳失敗';
            return $errors;
        }
        
        // 檢查文件大小（最大 10MB）
        $maxSize = 10 * 1024 * 1024; // 10MB
        if ($file['size'] > $maxSize) {
            $errors[] = '文件大小不能超過 10MB';
        }
        
        // 檢查文件類型
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!in_array($mimeType, $allowedTypes)) {
            $errors[] = '只支持 JPEG、PNG、GIF、WebP 格式的圖片';
        }
        
        // 檢查圖片尺寸
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            $errors[] = '無效的圖片文件';
        } else {
            $width = $imageInfo[0];
            $height = $imageInfo[1];
            
            // 最大尺寸限制
            if ($width > 4000 || $height > 4000) {
                $errors[] = '圖片尺寸不能超過 4000x4000 像素';
            }
        }
        
        return $errors;
    }

    /**
     * 生成安全的文件名
     */
    public static function generateSafeFilename($originalFilename)
    {
        $extension = pathinfo($originalFilename, PATHINFO_EXTENSION);
        $timestamp = date('Ymd_His');
        $random = bin2hex(random_bytes(8));
        
        return $timestamp . '_' . $random . '.' . strtolower($extension);
    }

    /**
     * 獲取圖片信息（包含尺寸）
     */
    public static function getImageInfo($filePath)
    {
        $imageInfo = getimagesize($filePath);
        if ($imageInfo === false) {
            return null;
        }
        
        return [
            'width' => $imageInfo[0],
            'height' => $imageInfo[1],
            'mime_type' => $imageInfo['mime']
        ];
    }
}
