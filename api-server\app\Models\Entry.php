<?php
require_once __DIR__ . '/BaseModel.php';

/**
 * 日記模型
 */
class Entry extends BaseModel {
    protected $table = 'entries';
    protected $fillable = [
        'user_id', 'content', 'emotion', 'tags', 'privacy',
        'is_anonymous', 'latitude', 'longitude', 'address', 'media'
    ];

    /**
     * 獲取用戶的日記
     */
    public function getUserEntries($userId, $page = 1, $limit = 20, $privacy = null) {
        $where = 'user_id = :user_id';
        $params = ['user_id' => $userId];
        
        if ($privacy) {
            $where .= ' AND privacy = :privacy';
            $params['privacy'] = $privacy;
        }
        
        return $this->paginate($page, $limit, 'created_at DESC', $where, $params);
    }

    /**
     * 獲取公開日記
     */
    public function getPublicEntries($page = 1, $limit = 20) {
        return $this->paginate($page, $limit, 'created_at DESC', 'privacy = :privacy', ['privacy' => 'public']);
    }

    /**
     * 獲取熱門日記
     */
    public function getPopularEntries($page = 1, $limit = 20, $days = 7) {
        $sql = "SELECT e.*, u.display_name, u.avatar,
                       (e.likes_count * 3 + e.comments_count * 2 + e.shares_count) as popularity_score
                FROM {$this->table} e
                JOIN users u ON e.user_id = u.id
                WHERE e.privacy = 'public' 
                AND e.created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
                ORDER BY popularity_score DESC, e.created_at DESC";
        
        return $this->db->paginate($sql, ['days' => $days], $page, $limit);
    }

    /**
     * 根據情感類型獲取日記
     */
    public function getEntriesByEmotion($emotion, $page = 1, $limit = 20) {
        return $this->paginate($page, $limit, 'created_at DESC', 'emotion = :emotion AND privacy = :privacy', [
            'emotion' => $emotion,
            'privacy' => 'public'
        ]);
    }

    /**
     * 根據標籤搜索日記
     */
    public function searchByTags($tags, $page = 1, $limit = 20) {
        if (is_string($tags)) {
            $tags = [$tags];
        }
        
        $conditions = [];
        $params = ['privacy' => 'public'];
        
        foreach ($tags as $index => $tag) {
            $conditions[] = "JSON_CONTAINS(tags, :tag_{$index})";
            $params["tag_{$index}"] = json_encode($tag);
        }
        
        $where = '(' . implode(' OR ', $conditions) . ') AND privacy = :privacy';
        
        return $this->paginate($page, $limit, 'created_at DESC', $where, $params);
    }

    /**
     * 搜索日記內容
     */
    public function searchEntries($searchTerm, $userId = null, $page = 1, $limit = 20) {
        $searchFields = ['content'];
        $additionalWhere = 'privacy = :privacy';
        $params = ['privacy' => 'public'];
        
        if ($userId) {
            $additionalWhere = 'user_id = :user_id';
            $params = ['user_id' => $userId];
        }
        
        return $this->db->search($this->table, $searchFields, $searchTerm, $additionalWhere, $params, $page, $limit);
    }

    /**
     * 獲取用戶今日日記
     */
    public function getTodayEntries($userId) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE user_id = :user_id 
                AND DATE(created_at) = CURDATE()
                ORDER BY created_at DESC";
        return $this->db->fetchAll($sql, ['user_id' => $userId]);
    }

    /**
     * 獲取用戶連續記錄天數
     */
    public function getUserStreak($userId) {
        $sql = "SELECT DISTINCT DATE(created_at) as entry_date 
                FROM {$this->table} 
                WHERE user_id = :user_id 
                ORDER BY entry_date DESC";
        
        $dates = $this->db->fetchAll($sql, ['user_id' => $userId]);
        
        if (empty($dates)) {
            return 0;
        }
        
        $streak = 0;
        $currentDate = new DateTime();
        $currentDate->setTime(0, 0, 0);
        
        foreach ($dates as $dateRow) {
            $entryDate = new DateTime($dateRow['entry_date']);
            $entryDate->setTime(0, 0, 0);
            
            $diff = $currentDate->diff($entryDate)->days;
            
            if ($diff == $streak) {
                $streak++;
                $currentDate->sub(new DateInterval('P1D'));
            } else {
                break;
            }
        }
        
        return $streak;
    }

    /**
     * 獲取日記統計
     */
    public function getEntryStats($userId = null, $days = 30) {
        $where = '';
        $params = ['days' => $days];
        
        if ($userId) {
            $where = 'WHERE user_id = :user_id AND';
            $params['user_id'] = $userId;
        } else {
            $where = 'WHERE';
        }
        
        $sql = "SELECT 
                    COUNT(*) as total_entries,
                    COUNT(DISTINCT DATE(created_at)) as active_days,
                    AVG(mood_score) as avg_mood_score,
                    AVG(gratitude_score) as avg_gratitude_score,
                    COUNT(DISTINCT emotion) as emotion_variety
                FROM {$this->table} 
                {$where} created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)";
        
        return $this->db->fetchOne($sql, $params);
    }

    /**
     * 獲取情感分布統計
     */
    public function getEmotionStats($userId = null, $days = 30) {
        $where = '';
        $params = ['days' => $days];
        
        if ($userId) {
            $where = 'WHERE user_id = :user_id AND';
            $params['user_id'] = $userId;
        } else {
            $where = 'WHERE';
        }
        
        $sql = "SELECT 
                    emotion,
                    COUNT(*) as count,
                    ROUND(COUNT(*) * 100.0 / (
                        SELECT COUNT(*) FROM {$this->table} 
                        {$where} created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
                    ), 2) as percentage
                FROM {$this->table} 
                {$where} created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
                GROUP BY emotion 
                ORDER BY count DESC";
        
        return $this->db->fetchAll($sql, $params);
    }

    /**
     * 獲取日記的互動數據
     */
    public function getEntryInteractions($entryId) {
        $sql = "SELECT 
                    COUNT(CASE WHEN type = 'like' THEN 1 END) as likes_count,
                    COUNT(CASE WHEN type = 'comment' THEN 1 END) as comments_count,
                    COUNT(CASE WHEN type = 'share' THEN 1 END) as shares_count
                FROM interactions 
                WHERE entry_id = :entry_id";
        
        return $this->db->fetchOne($sql, ['entry_id' => $entryId]);
    }

    /**
     * 更新日記互動計數
     */
    public function updateInteractionCounts($entryId) {
        $stats = $this->getEntryInteractions($entryId);
        
        return $this->update($entryId, [
            'likes_count' => $stats['likes_count'],
            'comments_count' => $stats['comments_count'],
            'shares_count' => $stats['shares_count']
        ]);
    }
}
