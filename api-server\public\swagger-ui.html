<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>感恩日記 API 文檔</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.10.5/swagger-ui.css" />
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        *, *:before, *:after {
            box-sizing: inherit;
        }
        body {
            margin:0;
            background: #fafafa;
        }
        .swagger-ui .topbar {
            background-color: #2196F3;
        }
        .swagger-ui .topbar .download-url-wrapper .select-label {
            color: white;
        }
        .swagger-ui .topbar .download-url-wrapper input[type=text] {
            border: 2px solid #1976D2;
        }
        .swagger-ui .info .title {
            color: #2196F3;
        }
        .custom-header {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        .custom-header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .custom-header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .api-info {
            background: white;
            padding: 20px;
            margin: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .api-info h3 {
            color: #2196F3;
            margin-top: 0;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px;
        }
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #2196F3;
        }
        .feature-card h4 {
            color: #2196F3;
            margin-top: 0;
        }
        .endpoint-count {
            background: #E3F2FD;
            color: #1976D2;
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="custom-header">
        <h1>🙏 感恩日記 API</h1>
        <p>完整的 RESTful API 文檔與測試界面</p>
        <div class="endpoint-count">8 個 API 端點</div>
    </div>

    <div class="api-info">
        <h3>📋 API 概述</h3>
        <p>感恩日記應用提供完整的 RESTful API，支持用戶認證、日記管理、社交互動等功能。</p>
        
        <h4>🔐 認證方式</h4>
        <p>API 使用 JWT Bearer Token 進行認證。在需要認證的端點中，請在 Authorization header 中包含 Bearer token。</p>
        
        <h4>📊 響應格式</h4>
        <ul>
            <li><strong>成功響應：</strong> <code>{"success": true, "data": {...}}</code></li>
            <li><strong>錯誤響應：</strong> <code>{"success": false, "error": "錯誤信息"}</code></li>
        </ul>
    </div>

    <div class="feature-list">
        <div class="feature-card">
            <h4>🔧 系統功能</h4>
            <ul>
                <li>健康檢查</li>
                <li>服務器狀態監控</li>
                <li>數據庫連接檢測</li>
            </ul>
        </div>
        
        <div class="feature-card">
            <h4>👤 用戶認證</h4>
            <ul>
                <li>用戶註冊</li>
                <li>用戶登入/登出</li>
                <li>用戶信息管理</li>
                <li>JWT Token 認證</li>
            </ul>
        </div>
        
        <div class="feature-card">
            <h4>📝 日記管理</h4>
            <ul>
                <li>創建感恩日記</li>
                <li>獲取公開日記</li>
                <li>獲取個人日記</li>
                <li>隱私設置控制</li>
            </ul>
        </div>
        
        <div class="feature-card">
            <h4>🎯 核心特色</h4>
            <ul>
                <li>情感標籤分類</li>
                <li>地理位置記錄</li>
                <li>標籤系統</li>
                <li>匿名發布選項</li>
            </ul>
        </div>
    </div>

    <div id="swagger-ui"></div>

    <script src="https://unpkg.com/swagger-ui-dist@5.10.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@5.10.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            // 獲取當前域名和端口
            const currentUrl = window.location.origin;

            const ui = SwaggerUIBundle({
                url: currentUrl + '/swagger.json',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                validatorUrl: null,
                tryItOutEnabled: true,
                supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
                onComplete: function() {
                    console.log('Swagger UI 加載完成');
                },
                onFailure: function(error) {
                    console.error('Swagger UI 加載失敗:', error);
                }
            });

            // 添加自定義樣式
            setTimeout(() => {
                const style = document.createElement('style');
                style.textContent = `
                    .swagger-ui .scheme-container {
                        background: #E3F2FD;
                        border: 1px solid #2196F3;
                        border-radius: 4px;
                        padding: 10px;
                        margin: 10px 0;
                    }
                    .swagger-ui .info .title {
                        font-size: 2em;
                        margin-bottom: 10px;
                    }
                    .swagger-ui .info .description {
                        font-size: 1.1em;
                        line-height: 1.6;
                    }
                    .swagger-ui .opblock.opblock-get {
                        border-color: #4CAF50;
                        background: rgba(76, 175, 80, 0.1);
                    }
                    .swagger-ui .opblock.opblock-post {
                        border-color: #2196F3;
                        background: rgba(33, 150, 243, 0.1);
                    }
                    .swagger-ui .opblock.opblock-put {
                        border-color: #FF9800;
                        background: rgba(255, 152, 0, 0.1);
                    }
                    .swagger-ui .opblock.opblock-delete {
                        border-color: #F44336;
                        background: rgba(244, 67, 54, 0.1);
                    }
                `;
                document.head.appendChild(style);
            }, 1000);
        };
    </script>
</body>
</html>
