<?php
// 簡單測試文件
echo "PHP is working!\n";

// 測試 autoload
if (file_exists('../vendor/autoload.php')) {
    echo "Autoload file exists\n";
    require_once '../vendor/autoload.php';
    echo "Autoload loaded successfully\n";
} else {
    echo "Autoload file not found\n";
}

// 測試 .env
if (file_exists('../.env')) {
    echo ".env file exists\n";
} else {
    echo ".env file not found\n";
}

// 測試 Dotenv
try {
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
    $dotenv->load();
    echo "Environment loaded successfully\n";
    echo "APP_NAME: " . ($_ENV['APP_NAME'] ?? 'Not set') . "\n";
} catch (Exception $e) {
    echo "Environment loading failed: " . $e->getMessage() . "\n";
}
