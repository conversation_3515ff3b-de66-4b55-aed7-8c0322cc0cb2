import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  AppBar,
  Toolbar,
  IconButton,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Avatar,
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import InfoIcon from '@mui/icons-material/Info';
import UpdateIcon from '@mui/icons-material/Update';
import GroupIcon from '@mui/icons-material/Group';
import PolicyIcon from '@mui/icons-material/Policy';
import SecurityIcon from '@mui/icons-material/Security';
import EmailIcon from '@mui/icons-material/Email';
import WebIcon from '@mui/icons-material/Web';
import FavoriteIcon from '@mui/icons-material/Favorite';
import StarIcon from '@mui/icons-material/Star';
import { motion } from 'framer-motion';

const AboutPage = () => {
  const navigate = useNavigate();
  const [showLicenseDialog, setShowLicenseDialog] = useState(false);
  const [showTeamDialog, setShowTeamDialog] = useState(false);

  const handleBack = () => {
    navigate('/app/settings');
  };

  const appInfo = {
    name: '感恩日記',
    version: '1.0.0',
    buildNumber: '2024.01.15',
    description: '記錄生活中的美好時刻，培養感恩的心',
    developer: '感恩日記團隊',
    website: 'https://gratitude-diary.app',
    email: '<EMAIL>',
  };

  const teamMembers = [
    {
      name: '張小明',
      role: '產品經理',
      avatar: '👨‍💼',
      description: '負責產品規劃和用戶體驗設計',
    },
    {
      name: '李小華',
      role: '前端開發',
      avatar: '👩‍💻',
      description: '負責應用界面和交互開發',
    },
    {
      name: '王小強',
      role: '後端開發',
      avatar: '👨‍💻',
      description: '負責服務器和數據庫開發',
    },
    {
      name: '陳小美',
      role: 'UI/UX 設計師',
      avatar: '👩‍🎨',
      description: '負責視覺設計和用戶體驗',
    },
  ];

  const aboutSections = [
    {
      title: '應用信息',
      items: [
        {
          icon: <InfoIcon />,
          title: '版本信息',
          subtitle: `${appInfo.version} (${appInfo.buildNumber})`,
          action: () => console.log('版本詳情'),
        },
        {
          icon: <UpdateIcon />,
          title: '更新日誌',
          subtitle: '查看最新功能和改進',
          action: () => console.log('更新日誌'),
        },
        {
          icon: <GroupIcon />,
          title: '開發團隊',
          subtitle: '了解我們的團隊成員',
          action: () => setShowTeamDialog(true),
        },
      ],
    },
    {
      title: '法律信息',
      items: [
        {
          icon: <PolicyIcon />,
          title: '使用條款',
          subtitle: '應用使用的條款和條件',
          action: () => console.log('使用條款'),
        },
        {
          icon: <SecurityIcon />,
          title: '隱私政策',
          subtitle: '了解我們如何保護您的隱私',
          action: () => console.log('隱私政策'),
        },
        {
          icon: <InfoIcon />,
          title: '開源許可',
          subtitle: '查看第三方庫的許可信息',
          action: () => setShowLicenseDialog(true),
        },
      ],
    },
    {
      title: '聯繫我們',
      items: [
        {
          icon: <EmailIcon />,
          title: '客服郵箱',
          subtitle: appInfo.email,
          action: () => window.open(`mailto:${appInfo.email}`),
        },
        {
          icon: <WebIcon />,
          title: '官方網站',
          subtitle: appInfo.website,
          action: () => window.open(appInfo.website),
        },
      ],
    },
  ];

  return (
    <Box sx={{
      bgcolor: 'background.default',
      minHeight: '100vh',
      width: '100%',
      maxWidth: '100vw',
      margin: '0 auto',
    }}>
      {/* 頂部導航欄 */}
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar sx={{ maxWidth: '600px', width: '100%', margin: '0 auto' }}>
          <IconButton onClick={handleBack} sx={{ mr: 2 }}>
            <ArrowBackIcon sx={{ color: 'text.primary' }} />
          </IconButton>
          <Typography
            variant="h3"
            sx={{
              flex: 1,
              color: 'text.primary',
              fontWeight: 600,
            }}
          >
            關於我們
          </Typography>
        </Toolbar>
      </AppBar>

      <Box sx={{
        p: 2,
        maxWidth: '600px',
        width: '100%',
        margin: '0 auto',
      }}>
        {/* 應用介紹卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card sx={{ 
            borderRadius: 4, 
            mb: 3,
            background: 'linear-gradient(135deg, #6B46C1 0%, #A78BFA 100%)',
            color: 'white',
          }}>
            <CardContent sx={{ p: 3, textAlign: 'center' }}>
              <Box sx={{ mb: 2 }}>
                <Typography variant="h2" sx={{ fontSize: '3rem', mb: 1 }}>
                  🙏
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                  {appInfo.name}
                </Typography>
                <Chip
                  label={`v${appInfo.version}`}
                  sx={{
                    bgcolor: 'rgba(255,255,255,0.2)',
                    color: 'white',
                    fontWeight: 600,
                  }}
                />
              </Box>
              <Typography variant="body1" sx={{ opacity: 0.9, lineHeight: 1.6 }}>
                {appInfo.description}
              </Typography>
              <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center', gap: 1 }}>
                <Chip
                  icon={<FavoriteIcon />}
                  label="用心製作"
                  sx={{
                    bgcolor: 'rgba(255,255,255,0.2)',
                    color: 'white',
                  }}
                />
                <Chip
                  icon={<StarIcon />}
                  label="持續改進"
                  sx={{
                    bgcolor: 'rgba(255,255,255,0.2)',
                    color: 'white',
                  }}
                />
              </Box>
            </CardContent>
          </Card>
        </motion.div>

        {/* 功能區塊 */}
        {aboutSections.map((section, sectionIndex) => (
          <motion.div
            key={section.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: sectionIndex * 0.1 + 0.1 }}
          >
            <Typography
              variant="h6"
              sx={{
                mb: 1,
                mt: sectionIndex > 0 ? 3 : 0,
                fontWeight: 600,
                color: 'text.secondary',
                fontSize: '0.875rem',
                textTransform: 'uppercase',
                letterSpacing: 0.5,
              }}
            >
              {section.title}
            </Typography>
            <Card sx={{ borderRadius: 3, mb: 2 }}>
              <List sx={{ py: 0 }}>
                {section.items.map((item, index) => (
                  <React.Fragment key={index}>
                    <ListItem
                      button
                      onClick={item.action}
                      sx={{
                        py: 2,
                        '&:hover': {
                          bgcolor: 'action.hover',
                        },
                      }}
                    >
                      <ListItemIcon sx={{ color: 'primary.main', minWidth: 40 }}>
                        {item.icon}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography variant="body1" sx={{ fontWeight: 500 }}>
                            {item.title}
                          </Typography>
                        }
                        secondary={
                          <Typography variant="body2" color="text.secondary">
                            {item.subtitle}
                          </Typography>
                        }
                      />
                    </ListItem>
                    {index < section.items.length - 1 && (
                      <Divider variant="inset" component="li" />
                    )}
                  </React.Fragment>
                ))}
              </List>
            </Card>
          </motion.div>
        ))}

        {/* 版權信息 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <Box sx={{ textAlign: 'center', mt: 4, mb: 2 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              © 2024 {appInfo.developer}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Made with ❤️ for a better world
            </Typography>
          </Box>
        </motion.div>
      </Box>

      {/* 團隊成員對話框 */}
      <Dialog
        open={showTeamDialog}
        onClose={() => setShowTeamDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>開發團隊</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            感謝以下團隊成員的辛勤付出，讓這個應用成為可能：
          </Typography>
          <List>
            {teamMembers.map((member, index) => (
              <React.Fragment key={index}>
                <ListItem sx={{ px: 0 }}>
                  <Avatar sx={{ mr: 2, bgcolor: 'primary.light' }}>
                    {member.avatar}
                  </Avatar>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          {member.name}
                        </Typography>
                        <Chip
                          label={member.role}
                          size="small"
                          sx={{
                            bgcolor: 'primary.light',
                            color: 'primary.main',
                            fontSize: '0.75rem',
                          }}
                        />
                      </Box>
                    }
                    secondary={member.description}
                  />
                </ListItem>
                {index < teamMembers.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowTeamDialog(false)}>關閉</Button>
        </DialogActions>
      </Dialog>

      {/* 開源許可對話框 */}
      <Dialog
        open={showLicenseDialog}
        onClose={() => setShowLicenseDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>開源許可</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            本應用使用了以下開源庫和框架：
          </Typography>
          <Box sx={{ fontFamily: 'monospace', fontSize: '0.875rem', bgcolor: 'grey.100', p: 2, borderRadius: 1 }}>
            <Typography component="pre" sx={{ whiteSpace: 'pre-wrap' }}>
{`React - MIT License
Material-UI - MIT License
React Router - MIT License
Framer Motion - MIT License
Vite - MIT License

感謝開源社區的貢獻！`}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowLicenseDialog(false)}>關閉</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AboutPage;
