/**
 * 路由保護組件
 * 確保只有已認證的用戶才能訪問受保護的路由
 */

import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthContext } from '../contexts/AuthContext.jsx';
import { CircularProgress, Box } from '@mui/material';

const ProtectedRoute = ({ children, requireAuth = true, redirectTo = '/login' }) => {
  const { isAuthenticated, loading } = useAuthContext();
  const location = useLocation();

  // 如果正在加載認證狀態，顯示加載指示器
  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <CircularProgress />
      </Box>
    );
  }

  // 如果需要認證但用戶未認證，重定向到登入頁面
  if (requireAuth && !isAuthenticated) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // 如果不需要認證但用戶已認證，可以重定向到主頁
  if (!requireAuth && isAuthenticated && location.pathname === '/login') {
    return <Navigate to="/" replace />;
  }

  // 渲染子組件
  return children;
};

export default ProtectedRoute;
