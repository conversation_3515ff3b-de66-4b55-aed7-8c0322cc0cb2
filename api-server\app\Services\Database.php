<?php
/**
 * 數據庫連接服務
 * 單例模式，提供統一的數據庫操作接口
 */
class Database {
    private static $instance = null;
    private $connection;
    private $config;

    private function __construct() {
        $this->config = require __DIR__ . '/../../config/database.php';
        $this->connect();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function connect() {
        try {
            $dsn = "mysql:host={$this->config['host']};port={$this->config['port']};dbname={$this->config['database']};charset={$this->config['charset']}";
            
            $this->connection = new PDO(
                $dsn, 
                $this->config['username'], 
                $this->config['password'], 
                $this->config['options']
            );
            
            // 設置錯誤模式
            $this->connection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }

    public function getConnection() {
        // 檢查連接是否還活著
        if (!$this->connection) {
            $this->connect();
        }
        return $this->connection;
    }

    /**
     * 執行查詢
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Query failed: " . $e->getMessage() . " SQL: " . $sql);
            throw new Exception("Query execution failed: " . $e->getMessage());
        }
    }

    /**
     * 獲取多行數據
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * 獲取單行數據
     */
    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * 獲取單個值
     */
    public function fetchColumn($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchColumn();
    }

    /**
     * 插入數據
     */
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->query($sql, $data);
        
        return $this->connection->lastInsertId();
    }

    /**
     * 更新數據
     */
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $key) {
            $setClause[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setClause);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $whereParams);
        
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * 刪除數據
     */
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * 開始事務
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }

    /**
     * 提交事務
     */
    public function commit() {
        return $this->connection->commit();
    }

    /**
     * 回滾事務
     */
    public function rollback() {
        return $this->connection->rollback();
    }

    /**
     * 檢查表是否存在
     */
    public function tableExists($tableName) {
        $sql = "SHOW TABLES LIKE :table";
        $result = $this->fetchOne($sql, ['table' => $tableName]);
        return !empty($result);
    }

    /**
     * 獲取表結構
     */
    public function getTableStructure($tableName) {
        $sql = "DESCRIBE {$tableName}";
        return $this->fetchAll($sql);
    }

    /**
     * 執行原生SQL文件
     */
    public function executeSqlFile($filePath) {
        if (!file_exists($filePath)) {
            throw new Exception("SQL file not found: {$filePath}");
        }

        $sql = file_get_contents($filePath);
        $statements = explode(';', $sql);

        $this->beginTransaction();
        try {
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    $this->connection->exec($statement);
                }
            }
            $this->commit();
            return true;
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * 獲取數據庫統計信息
     */
    public function getDatabaseStats() {
        $stats = [];
        
        // 獲取表大小
        $sql = "
            SELECT 
                table_name,
                table_rows,
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
            FROM information_schema.tables 
            WHERE table_schema = :database
            ORDER BY size_mb DESC
        ";
        $stats['tables'] = $this->fetchAll($sql, ['database' => $this->config['database']]);
        
        // 獲取總大小
        $sql = "
            SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS total_size_mb
            FROM information_schema.tables 
            WHERE table_schema = :database
        ";
        $stats['total_size'] = $this->fetchColumn($sql, ['database' => $this->config['database']]);
        
        return $stats;
    }

    /**
     * 分頁查詢
     */
    public function paginate($sql, $params = [], $page = 1, $limit = 20) {
        // 計算總數
        $countSql = "SELECT COUNT(*) FROM ({$sql}) as count_table";
        $total = $this->fetchColumn($countSql, $params);
        
        // 計算分頁
        $offset = ($page - 1) * $limit;
        $paginatedSql = $sql . " LIMIT {$limit} OFFSET {$offset}";
        
        $data = $this->fetchAll($paginatedSql, $params);
        
        return [
            'data' => $data,
            'pagination' => [
                'page' => (int)$page,
                'limit' => (int)$limit,
                'total' => (int)$total,
                'total_pages' => (int)ceil($total / $limit),
                'has_next' => $page < ceil($total / $limit),
                'has_prev' => $page > 1
            ]
        ];
    }

    /**
     * 搜索功能
     */
    public function search($table, $searchFields, $searchTerm, $additionalWhere = '', $params = [], $page = 1, $limit = 20) {
        $searchConditions = [];
        $searchParams = [];
        
        foreach ($searchFields as $field) {
            $searchConditions[] = "{$field} LIKE :search_{$field}";
            $searchParams["search_{$field}"] = "%{$searchTerm}%";
        }
        
        $whereClause = '(' . implode(' OR ', $searchConditions) . ')';
        if (!empty($additionalWhere)) {
            $whereClause .= " AND {$additionalWhere}";
        }
        
        $sql = "SELECT * FROM {$table} WHERE {$whereClause} ORDER BY created_at DESC";
        $allParams = array_merge($searchParams, $params);
        
        return $this->paginate($sql, $allParams, $page, $limit);
    }

    /**
     * 批量插入
     */
    public function batchInsert($table, $data) {
        if (empty($data)) {
            return 0;
        }

        $columns = array_keys($data[0]);
        $columnList = implode(',', $columns);
        
        $placeholders = [];
        $values = [];
        
        foreach ($data as $index => $row) {
            $rowPlaceholders = [];
            foreach ($columns as $column) {
                $placeholder = ":{$column}_{$index}";
                $rowPlaceholders[] = $placeholder;
                $values[$placeholder] = $row[$column];
            }
            $placeholders[] = '(' . implode(',', $rowPlaceholders) . ')';
        }
        
        $sql = "INSERT INTO {$table} ({$columnList}) VALUES " . implode(',', $placeholders);
        
        $stmt = $this->query($sql, $values);
        return $stmt->rowCount();
    }

    /**
     * 清理舊數據
     */
    public function cleanupOldData($table, $dateColumn, $days) {
        $sql = "DELETE FROM {$table} WHERE {$dateColumn} < DATE_SUB(NOW(), INTERVAL :days DAY)";
        $stmt = $this->query($sql, ['days' => $days]);
        return $stmt->rowCount();
    }

    /**
     * 獲取連接狀態
     */
    public function getConnectionStatus() {
        try {
            $this->connection->query('SELECT 1');
            return true;
        } catch (PDOException $e) {
            return false;
        }
    }

    /**
     * 關閉連接
     */
    public function close() {
        $this->connection = null;
    }

    /**
     * 析構函數
     */
    public function __destruct() {
        $this->close();
    }
}
