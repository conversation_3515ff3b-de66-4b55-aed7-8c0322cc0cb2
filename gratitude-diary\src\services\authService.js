/**
 * 認證服務
 * 處理用戶認證相關的 API 請求
 */

import { apiClient } from './api.js';

class AuthService {
  constructor() {
    this.currentUser = null;
    this.isInitialized = false;
    this.initializeAuth();
  }

  /**
   * 初始化認證狀態
   */
  async initializeAuth() {
    const token = localStorage.getItem('auth_token');
    if (token) {
      try {
        // 驗證 token 並獲取用戶信息
        const userData = await this.getCurrentUser();
        this.currentUser = userData.user;
        apiClient.setToken(token);
      } catch (error) {
        // Token 無效，清除本地存儲
        this.logout();
      }
    }
    this.isInitialized = true;
  }

  /**
   * 用戶註冊
   */
  async register(userData) {
    try {
      const response = await apiClient.post('/auth/register', userData);
      
      if (response.success) {
        // 保存 token 和用戶信息
        apiClient.setToken(response.token);
        this.currentUser = response.user;
        
        return {
          success: true,
          user: response.user,
          message: response.message
        };
      }
      
      throw new Error(response.error || '註冊失敗');
    } catch (error) {
      return {
        success: false,
        error: error.message || '註冊失敗'
      };
    }
  }

  /**
   * 用戶登入
   */
  async login(credentials) {
    try {
      const response = await apiClient.post('/auth/login', credentials);
      
      if (response.success) {
        // 保存 token 和用戶信息
        apiClient.setToken(response.token);
        this.currentUser = response.user;
        
        return {
          success: true,
          user: response.user,
          message: response.message
        };
      }
      
      throw new Error(response.error || '登入失敗');
    } catch (error) {
      return {
        success: false,
        error: error.message || '登入失敗'
      };
    }
  }

  /**
   * 用戶登出
   */
  async logout() {
    try {
      // 調用後端登出 API
      await apiClient.post('/auth/logout');
    } catch (error) {
      console.warn('Logout API call failed:', error);
    } finally {
      // 清除本地狀態
      apiClient.setToken(null);
      this.currentUser = null;
      
      // 觸發登出事件
      window.dispatchEvent(new CustomEvent('auth:logout'));
    }
  }

  /**
   * 獲取當前用戶信息
   */
  async getCurrentUser() {
    try {
      const response = await apiClient.get('/auth/me');
      
      if (response.success) {
        this.currentUser = response.user;
        return response;
      }
      
      throw new Error(response.error || '獲取用戶信息失敗');
    } catch (error) {
      throw error;
    }
  }

  /**
   * 更新用戶資料
   */
  async updateProfile(profileData) {
    try {
      const response = await apiClient.put('/auth/profile', profileData);
      
      if (response.success) {
        this.currentUser = response.user;
        return {
          success: true,
          user: response.user,
          message: response.message
        };
      }
      
      throw new Error(response.error || '更新資料失敗');
    } catch (error) {
      return {
        success: false,
        error: error.message || '更新資料失敗'
      };
    }
  }

  /**
   * 更改密碼
   */
  async changePassword(passwordData) {
    try {
      const response = await apiClient.put('/auth/password', passwordData);
      
      if (response.success) {
        return {
          success: true,
          message: response.message
        };
      }
      
      throw new Error(response.error || '更改密碼失敗');
    } catch (error) {
      return {
        success: false,
        error: error.message || '更改密碼失敗'
      };
    }
  }

  /**
   * 檢查是否已認證
   */
  isAuthenticated() {
    return !!this.currentUser && !!apiClient.getToken();
  }

  /**
   * 獲取當前用戶
   */
  getUser() {
    return this.currentUser;
  }

  /**
   * 獲取用戶 ID
   */
  getUserId() {
    return this.currentUser?.id;
  }

  /**
   * 檢查是否為管理員
   */
  isAdmin() {
    return this.currentUser?.role === 'admin';
  }

  /**
   * 等待認證初始化完成
   */
  async waitForInitialization() {
    if (this.isInitialized) {
      return;
    }
    
    return new Promise((resolve) => {
      const checkInitialized = () => {
        if (this.isInitialized) {
          resolve();
        } else {
          setTimeout(checkInitialized, 100);
        }
      };
      checkInitialized();
    });
  }
}

// 創建全局認證服務實例
export const authService = new AuthService();

// 默認導出
export default authService;
