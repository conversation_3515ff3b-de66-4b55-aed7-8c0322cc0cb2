/**
 * 日記服務
 * 處理日記相關的 API 請求
 */

import { apiClient } from './api.js';

class EntryService {
  /**
   * 獲取日記列表
   */
  async getEntries(params = {}) {
    try {
      const response = await apiClient.get('/api/v1/entries', params);
      
      if (response.success) {
        return {
          success: true,
          entries: response.entries
        };
      }
      
      throw new Error(response.error || '獲取日記列表失敗');
    } catch (error) {
      return {
        success: false,
        error: error.message || '獲取日記列表失敗'
      };
    }
  }

  /**
   * 獲取公開日記
   */
  async getPublicEntries(params = {}) {
    try {
      const response = await apiClient.get('/api/v1/entries/public', params);
      
      if (response.success) {
        return {
          success: true,
          entries: response.entries
        };
      }
      
      throw new Error(response.error || '獲取公開日記失敗');
    } catch (error) {
      return {
        success: false,
        error: error.message || '獲取公開日記失敗'
      };
    }
  }

  /**
   * 獲取熱門日記
   */
  async getPopularEntries(params = {}) {
    try {
      const response = await apiClient.get('/api/v1/entries/popular', params);
      
      if (response.success) {
        return {
          success: true,
          entries: response.entries
        };
      }
      
      throw new Error(response.error || '獲取熱門日記失敗');
    } catch (error) {
      return {
        success: false,
        error: error.message || '獲取熱門日記失敗'
      };
    }
  }

  /**
   * 獲取單個日記
   */
  async getEntry(entryId) {
    try {
      const response = await apiClient.get(`/api/v1/entries/${entryId}`);
      
      if (response.success) {
        return {
          success: true,
          entry: response.entry
        };
      }
      
      throw new Error(response.error || '獲取日記失敗');
    } catch (error) {
      return {
        success: false,
        error: error.message || '獲取日記失敗'
      };
    }
  }

  /**
   * 創建日記
   */
  async createEntry(entryData) {
    try {
      const response = await apiClient.post('/api/v1/entries', entryData);
      
      if (response.success) {
        return {
          success: true,
          entry: response.entry,
          message: response.message
        };
      }
      
      throw new Error(response.error || '創建日記失敗');
    } catch (error) {
      return {
        success: false,
        error: error.message || '創建日記失敗'
      };
    }
  }

  /**
   * 更新日記
   */
  async updateEntry(entryId, entryData) {
    try {
      const response = await apiClient.put(`/api/v1/entries/${entryId}`, entryData);

      if (response.success) {
        return {
          success: true,
          entry: response.entry,
          message: response.message
        };
      }

      throw new Error(response.error || '更新日記失敗');
    } catch (error) {
      return {
        success: false,
        error: error.message || '更新日記失敗'
      };
    }
  }

  /**
   * 刪除日記
   */
  async deleteEntry(entryId) {
    try {
      const response = await apiClient.delete(`/api/v1/entries/${entryId}`);
      
      if (response.success) {
        return {
          success: true,
          message: response.message
        };
      }
      
      throw new Error(response.error || '刪除日記失敗');
    } catch (error) {
      return {
        success: false,
        error: error.message || '刪除日記失敗'
      };
    }
  }

  /**
   * 搜索日記
   */
  async searchEntries(searchTerm, params = {}) {
    try {
      const searchParams = {
        q: searchTerm,
        ...params
      };
      
      const response = await apiClient.get('/api/v1/entries/search', searchParams);
      
      if (response.success) {
        return {
          success: true,
          results: response.results
        };
      }
      
      throw new Error(response.error || '搜索日記失敗');
    } catch (error) {
      return {
        success: false,
        error: error.message || '搜索日記失敗'
      };
    }
  }

  /**
   * 獲取統計數據
   */
  async getStats(params = {}) {
    try {
      const response = await apiClient.get('/api/v1/stats', params);
      
      if (response.success) {
        return {
          success: true,
          stats: response.stats,
          emotions: response.emotions,
          streak: response.streak
        };
      }
      
      throw new Error(response.error || '獲取統計數據失敗');
    } catch (error) {
      return {
        success: false,
        error: error.message || '獲取統計數據失敗'
      };
    }
  }

  /**
   * 點讚日記
   */
  async likeEntry(entryId) {
    try {
      const response = await apiClient.post(`/api/v1/entries/${entryId}/like`);
      
      if (response.success) {
        return {
          success: true,
          message: response.message
        };
      }
      
      throw new Error(response.error || '點讚失敗');
    } catch (error) {
      return {
        success: false,
        error: error.message || '點讚失敗'
      };
    }
  }

  /**
   * 評論日記
   */
  async commentEntry(entryId, comment) {
    try {
      const response = await apiClient.post(`/api/v1/entries/${entryId}/comment`, {
        content: comment
      });
      
      if (response.success) {
        return {
          success: true,
          comment: response.comment,
          message: response.message
        };
      }
      
      throw new Error(response.error || '評論失敗');
    } catch (error) {
      return {
        success: false,
        error: error.message || '評論失敗'
      };
    }
  }

  /**
   * 分享日記
   */
  async shareEntry(entryId) {
    try {
      const response = await apiClient.post(`/api/v1/entries/${entryId}/share`);
      
      if (response.success) {
        return {
          success: true,
          shareUrl: response.shareUrl,
          message: response.message
        };
      }
      
      throw new Error(response.error || '分享失敗');
    } catch (error) {
      return {
        success: false,
        error: error.message || '分享失敗'
      };
    }
  }
}

// 創建全局日記服務實例
export const entryService = new EntryService();

// 默認導出
export default entryService;
