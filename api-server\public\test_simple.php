<?php
// 簡單的 API 測試文件
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept, Origin');

// 處理 OPTIONS 請求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 載入必要的文件
require_once '../vendor/autoload.php';

use Dotenv\Dotenv;

try {
    // 載入環境變數
    $dotenv = Dotenv::createImmutable(__DIR__ . '/..');
    $dotenv->load();

    // 測試數據庫連接
    require_once '../app/Services/Database.php';
    $db = Database::getInstance();
    $result = $db->fetchOne("SELECT COUNT(*) as count FROM users");

    echo json_encode([
        'success' => true,
        'message' => 'API is working',
        'timestamp' => date('Y-m-d H:i:s'),
        'user_count' => $result['count'],
        'version' => '1.0.0'
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
