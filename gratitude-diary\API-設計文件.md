# 感恩日記 API 設計文件

## 專案概述

感恩日記是一個多平台的感恩記錄與社群分享應用，支援Web、移動端等多種客戶端。本API採用RESTful設計，使用Firebase作為後端服務，提供完整的用戶管理、內容管理、社群互動等功能。

## 技術架構

### 後端技術棧
- **Firebase Authentication**: 用戶認證與授權
- **Cloud Firestore**: NoSQL數據庫
- **Firebase Storage**: 文件存儲（圖片、音頻）
- **Firebase Functions**: 服務端邏輯
- **Firebase Hosting**: 靜態資源託管
- **Firebase Cloud Messaging**: 推播通知

### API設計原則
- RESTful API設計
- JWT Token認證
- 統一的響應格式
- 完整的錯誤處理
- API版本控制
- 請求限流與安全防護

## 數據模型設計

### 1. 用戶模型 (Users)
```javascript
{
  uid: "string",                    // Firebase UID
  email: "string",                  // 電子郵件
  displayName: "string",            // 顯示名稱
  avatar: "string",                 // 頭像URL
  bio: "string",                    // 個人簡介
  preferences: {
    language: "zh-TW",              // 語言偏好
    timezone: "Asia/Taipei",        // 時區
    theme: "light",                 // 主題模式
    privacy: {
      profileVisible: true,         // 個人資料可見性
      allowComments: true,          // 允許評論
      allowMessages: false          // 允許私訊
    },
    notifications: {
      dailyReminder: true,          // 每日提醒
      focusReminder: true,          // 專注提醒
      socialInteraction: true,      // 社群互動通知
      pushEnabled: true             // 推播通知
    }
  },
  stats: {
    totalEntries: 0,                // 總日記數
    totalLikes: 0,                  // 總獲讚數
    totalComments: 0,               // 總評論數
    focusSessions: 0,               // 專注次數
    focusMinutes: 0,                // 專注總時長
    streakDays: 0,                  // 連續記錄天數
    joinDate: "timestamp",          // 加入日期
    lastActiveDate: "timestamp"     // 最後活躍日期
  },
  achievements: [                   // 成就列表
    {
      id: "string",
      unlockedAt: "timestamp"
    }
  ],
  createdAt: "timestamp",
  updatedAt: "timestamp"
}
```

### 2. 日記條目模型 (Entries)
```javascript
{
  id: "string",                     // 條目ID
  userId: "string",                 // 作者ID
  content: "string",                // 日記內容
  emotion: "string",                // 情感類型
  tags: ["string"],                 // 標籤列表
  media: [                          // 媒體附件
    {
      type: "image|audio",
      url: "string",
      thumbnail: "string",          // 縮圖URL
      metadata: {
        size: "number",
        duration: "number"          // 音頻時長
      }
    }
  ],
  privacy: "public|friends|private", // 隱私設定
  location: {                       // 位置信息（可選）
    latitude: "number",
    longitude: "number",
    address: "string"
  },
  interactions: {
    likes: 0,                       // 點讚數
    comments: 0,                    // 評論數
    shares: 0                       // 分享數
  },
  isAnonymous: false,               // 是否匿名發布
  createdAt: "timestamp",
  updatedAt: "timestamp"
}
```

### 3. 專注工作記錄模型 (FocusSessions)
```javascript
{
  id: "string",
  userId: "string",
  workDuration: 25,                 // 工作時長（分鐘）
  breakDuration: 10,                // 休息時長（分鐘）
  actualWorkTime: 25,               // 實際工作時間
  gratitudeMessage: "string",       // 感謝詞
  isCompleted: true,                // 是否完成
  startTime: "timestamp",
  endTime: "timestamp",
  createdAt: "timestamp"
}
```

### 4. 社群互動模型 (Interactions)
```javascript
// 點讚記錄
{
  id: "string",
  userId: "string",
  entryId: "string",
  type: "like",
  createdAt: "timestamp"
}

// 評論記錄
{
  id: "string",
  userId: "string",
  entryId: "string",
  content: "string",
  parentId: "string",               // 回覆的評論ID
  type: "comment",
  createdAt: "timestamp"
}
```

## API端點設計

### 基礎配置
- **Base URL**: `https://api.gratitude-diary.com/v1`
- **認證方式**: Bearer Token (Firebase JWT)
- **內容類型**: `application/json`
- **字符編碼**: UTF-8

### 1. 用戶認證 API

#### 1.1 用戶註冊
```http
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "displayName": "張小明"
}

Response:
{
  "success": true,
  "data": {
    "user": {
      "uid": "firebase_uid",
      "email": "<EMAIL>",
      "displayName": "張小明",
      "token": "jwt_token"
    }
  },
  "message": "註冊成功"
}
```

#### 1.2 用戶登入
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

Response:
{
  "success": true,
  "data": {
    "user": {
      "uid": "firebase_uid",
      "email": "<EMAIL>",
      "displayName": "張小明",
      "token": "jwt_token"
    }
  },
  "message": "登入成功"
}
```

#### 1.3 第三方登入（Google、Facebook、Apple）
```http
POST /auth/social
Content-Type: application/json

{
  "provider": "google",
  "token": "social_provider_token"
}
```

#### 1.4 登出
```http
POST /auth/logout
Authorization: Bearer {token}

Response:
{
  "success": true,
  "message": "登出成功"
}
```

#### 1.5 刷新Token
```http
POST /auth/refresh
Content-Type: application/json

{
  "refreshToken": "refresh_token"
}
```

### 2. 用戶管理 API

#### 2.1 獲取用戶資料
```http
GET /users/profile
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "user": {
      // 完整用戶模型數據
    }
  }
}
```

#### 2.2 更新用戶資料
```http
PUT /users/profile
Authorization: Bearer {token}
Content-Type: application/json

{
  "displayName": "新的顯示名稱",
  "bio": "個人簡介",
  "preferences": {
    "language": "zh-TW",
    "theme": "dark"
  }
}
```

#### 2.3 上傳頭像
```http
POST /users/avatar
Authorization: Bearer {token}
Content-Type: multipart/form-data

FormData:
- avatar: File
```

#### 2.4 獲取用戶統計
```http
GET /users/stats?period=week|month|year
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "stats": {
      "totalEntries": 18,
      "totalLikes": 156,
      "focusSessions": 45,
      "streakDays": 12,
      "emotionDistribution": {
        "gratitude": 35,
        "joy": 20,
        "peace": 18
      }
    }
  }
}
```

### 3. 日記管理 API

#### 3.1 創建日記
```http
POST /entries
Authorization: Bearer {token}
Content-Type: application/json

{
  "content": "今天很感恩...",
  "emotion": "gratitude",
  "tags": ["工作", "家庭"],
  "privacy": "public",
  "isAnonymous": false
}

Response:
{
  "success": true,
  "data": {
    "entry": {
      "id": "entry_id",
      // 完整日記模型數據
    }
  },
  "message": "日記創建成功"
}
```

#### 3.2 獲取日記列表
```http
GET /entries?page=1&limit=20&emotion=gratitude&privacy=public&userId=user_id
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "entries": [
      // 日記列表
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5
    }
  }
}
```

#### 3.3 獲取單個日記
```http
GET /entries/{entryId}
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "entry": {
      // 完整日記數據
    }
  }
}
```

#### 3.4 更新日記
```http
PUT /entries/{entryId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "content": "更新的內容",
  "emotion": "joy",
  "tags": ["更新", "標籤"]
}
```

#### 3.5 刪除日記
```http
DELETE /entries/{entryId}
Authorization: Bearer {token}

Response:
{
  "success": true,
  "message": "日記刪除成功"
}
```

#### 3.6 上傳媒體附件
```http
POST /entries/{entryId}/media
Authorization: Bearer {token}
Content-Type: multipart/form-data

FormData:
- media: File
- type: "image" | "audio"
```

### 4. 社群互動 API

#### 4.1 點讚/取消點讚
```http
POST /entries/{entryId}/like
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "liked": true,
    "likeCount": 13
  }
}
```

#### 4.2 添加評論
```http
POST /entries/{entryId}/comments
Authorization: Bearer {token}
Content-Type: application/json

{
  "content": "很棒的分享！",
  "parentId": "comment_id"  // 可選，回覆評論
}
```

#### 4.3 獲取評論列表
```http
GET /entries/{entryId}/comments?page=1&limit=10
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "comments": [
      {
        "id": "comment_id",
        "userId": "user_id",
        "content": "評論內容",
        "user": {
          "displayName": "用戶名",
          "avatar": "avatar_url"
        },
        "createdAt": "timestamp"
      }
    ]
  }
}
```

#### 4.4 分享日記
```http
POST /entries/{entryId}/share
Authorization: Bearer {token}
Content-Type: application/json

{
  "platform": "facebook|twitter|line|copy"
}
```

### 5. 專注工作 API

#### 5.1 開始專注工作
```http
POST /focus/start
Authorization: Bearer {token}
Content-Type: application/json

{
  "workDuration": 25,
  "breakDuration": 10,
  "gratitudeMessage": "感謝詞模板"
}

Response:
{
  "success": true,
  "data": {
    "sessionId": "session_id",
    "startTime": "timestamp"
  }
}
```

#### 5.2 完成專注工作
```http
POST /focus/{sessionId}/complete
Authorization: Bearer {token}
Content-Type: application/json

{
  "actualWorkTime": 25,
  "isCompleted": true
}
```

#### 5.3 獲取專注統計
```http
GET /focus/stats?period=week|month|year
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "totalSessions": 45,
    "totalMinutes": 1350,
    "averageSession": 30,
    "dailyStats": [
      {
        "date": "2024-01-01",
        "sessions": 3,
        "minutes": 90
      }
    ]
  }
}
```

### 6. 設定管理 API

#### 6.1 獲取用戶設定
```http
GET /settings
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "preferences": {
      // 用戶偏好設定
    },
    "notifications": {
      // 通知設定
    }
  }
}
```

#### 6.2 更新設定
```http
PUT /settings
Authorization: Bearer {token}
Content-Type: application/json

{
  "preferences": {
    "theme": "dark",
    "language": "zh-TW"
  },
  "notifications": {
    "dailyReminder": true,
    "pushEnabled": false
  }
}
```

### 7. 數據分析 API

#### 7.1 獲取情感分析
```http
GET /analytics/emotions?period=week|month|year
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "distribution": {
      "gratitude": 35,
      "joy": 20,
      "peace": 18
    },
    "trends": [
      {
        "date": "2024-01-01",
        "emotions": {
          "gratitude": 5,
          "joy": 2
        }
      }
    ]
  }
}
```

#### 7.2 獲取活躍度數據
```http
GET /analytics/activity?period=month
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "dailyEntries": [1, 0, 2, 1, 1, 0, 1],
    "weeklyTrend": "+15%",
    "streakDays": 12
  }
}
```

### 8. 成就系統 API

#### 8.1 獲取成就列表
```http
GET /achievements
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "achievements": [
      {
        "id": "first_entry",
        "name": "初心者",
        "description": "發布第一篇日記",
        "icon": "🌱",
        "isUnlocked": true,
        "unlockedAt": "timestamp"
      }
    ]
  }
}
```

### 9. 通知系統 API

#### 9.1 獲取通知列表
```http
GET /notifications?page=1&limit=20&type=like|comment|achievement
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "notifications": [
      {
        "id": "notification_id",
        "type": "like",
        "title": "有人點讚了你的日記",
        "content": "張小明點讚了你的日記",
        "isRead": false,
        "createdAt": "timestamp"
      }
    ]
  }
}
```

#### 9.2 標記通知為已讀
```http
PUT /notifications/{notificationId}/read
Authorization: Bearer {token}
```

### 10. 搜索 API

#### 10.1 搜索日記
```http
GET /search/entries?q=關鍵字&emotion=gratitude&tags=工作&page=1&limit=20
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "entries": [
      // 搜索結果
    ],
    "pagination": {
      "page": 1,
      "total": 50
    }
  }
}
```

## 錯誤處理

### 統一錯誤響應格式
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "錯誤描述",
    "details": "詳細錯誤信息"
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 常見錯誤代碼
- `AUTH_REQUIRED`: 需要認證
- `AUTH_INVALID`: 認證無效
- `PERMISSION_DENIED`: 權限不足
- `RESOURCE_NOT_FOUND`: 資源不存在
- `VALIDATION_ERROR`: 數據驗證錯誤
- `RATE_LIMIT_EXCEEDED`: 請求頻率超限
- `SERVER_ERROR`: 服務器內部錯誤

## 安全性考量

### 1. 認證與授權
- 使用Firebase Authentication進行用戶認證
- JWT Token有效期設置為24小時
- 實施Refresh Token機制
- 支援多因素認證（MFA）

### 2. 數據保護
- 所有API請求使用HTTPS
- 敏感數據加密存儲
- 實施數據脫敏處理
- 定期數據備份

### 3. 請求限流
- 每個用戶每分鐘最多100個請求
- 上傳文件大小限制：圖片10MB，音頻20MB
- 實施IP白名單機制

### 4. 隱私保護
- 支援用戶數據導出
- 支援用戶數據刪除
- 遵循GDPR等隱私法規
- 匿名化處理用戶數據
