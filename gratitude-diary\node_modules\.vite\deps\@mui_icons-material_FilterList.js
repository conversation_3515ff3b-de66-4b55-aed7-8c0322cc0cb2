"use client";
import "./chunk-C6WWHQR7.js";
import {
  createSvgIcon
} from "./chunk-QVI57TQV.js";
import "./chunk-YZ6PAUYO.js";
import "./chunk-BRYCWNNY.js";
import "./chunk-6YD57FQ6.js";
import {
  require_jsx_runtime
} from "./chunk-3NBMPMSA.js";
import {
  __toESM
} from "./chunk-DAFPDBRK.js";

// node_modules/@mui/icons-material/esm/FilterList.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var FilterList_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "M10 18h4v-2h-4zM3 6v2h18V6zm3 7h12v-2H6z"
}), "FilterList");
export {
  FilterList_default as default
};
//# sourceMappingURL=@mui_icons-material_FilterList.js.map
