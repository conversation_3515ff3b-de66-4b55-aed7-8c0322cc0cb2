<?php
/**
 * API 路由定義
 */

// 載入必要的類
require_once __DIR__ . '/../app/Controllers/AuthController.php';
require_once __DIR__ . '/../app/Controllers/EntryController.php';
require_once __DIR__ . '/../app/Middleware/AuthMiddleware.php';
require_once __DIR__ . '/../app/Middleware/CorsMiddleware.php';

/**
 * 簡單路由器類
 */
class Router {
    private $routes = [];
    private $middlewares = [];

    /**
     * 添加路由
     */
    public function addRoute($method, $path, $handler, $middlewares = []) {
        $this->routes[] = [
            'method' => strtoupper($method),
            'path' => $path,
            'handler' => $handler,
            'middlewares' => $middlewares
        ];
    }

    /**
     * GET 路由
     */
    public function get($path, $handler, $middlewares = []) {
        $this->addRoute('GET', $path, $handler, $middlewares);
    }

    /**
     * POST 路由
     */
    public function post($path, $handler, $middlewares = []) {
        $this->addRoute('POST', $path, $handler, $middlewares);
    }

    /**
     * PUT 路由
     */
    public function put($path, $handler, $middlewares = []) {
        $this->addRoute('PUT', $path, $handler, $middlewares);
    }

    /**
     * DELETE 路由
     */
    public function delete($path, $handler, $middlewares = []) {
        $this->addRoute('DELETE', $path, $handler, $middlewares);
    }

    /**
     * 處理請求
     */
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        
        // 移除基礎路徑
        $basePath = '/diary/app/api-server/public';
        if (strpos($path, $basePath) === 0) {
            $path = substr($path, strlen($basePath));
        }
        
        // 確保路徑以 / 開頭
        if (!$path || $path[0] !== '/') {
            $path = '/' . $path;
        }

        foreach ($this->routes as $route) {
            if ($route['method'] === $method && $this->matchPath($route['path'], $path)) {
                // 執行中間件
                foreach ($route['middlewares'] as $middleware) {
                    if (!call_user_func($middleware)) {
                        return; // 中間件阻止了請求
                    }
                }

                // 提取路徑參數
                $params = $this->extractParams($route['path'], $path);
                
                // 執行處理器
                if (is_array($route['handler'])) {
                    $controller = $route['handler'][0];
                    $method = $route['handler'][1];
                    call_user_func_array([$controller, $method], $params);
                } else {
                    call_user_func_array($route['handler'], $params);
                }
                return;
            }
        }

        // 404 Not Found
        $this->notFound();
    }

    /**
     * 匹配路徑
     */
    private function matchPath($routePath, $requestPath) {
        // 將路由路徑轉換為正則表達式
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $routePath);
        $pattern = '#^' . $pattern . '$#';
        
        return preg_match($pattern, $requestPath);
    }

    /**
     * 提取路徑參數
     */
    private function extractParams($routePath, $requestPath) {
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $routePath);
        $pattern = '#^' . $pattern . '$#';
        
        if (preg_match($pattern, $requestPath, $matches)) {
            array_shift($matches); // 移除完整匹配
            return $matches;
        }
        
        return [];
    }

    /**
     * 404 響應
     */
    private function notFound() {
        http_response_code(404);
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'error' => 'Route not found',
            'message' => 'The requested endpoint does not exist'
        ]);
    }
}

// 創建路由器實例
$router = new Router();

// 創建控制器實例
$authController = new AuthController();
$entryController = new EntryController();

// ===== 公開路由 (不需要認證) =====

// 健康檢查
$router->get('/health', function() {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => 'API is running',
        'timestamp' => date('Y-m-d H:i:s'),
        'version' => '1.0.0'
    ]);
});

$router->get('/api/v1/health', function() {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => 'API is running',
        'timestamp' => date('Y-m-d H:i:s'),
        'version' => '1.0.0'
    ]);
});

// 認證路由
$router->post('/api/v1/auth/register', [$authController, 'register']);
$router->post('/api/v1/auth/login', [$authController, 'login']);
$router->post('/api/v1/auth/logout', [$authController, 'logout']);

// 公開日記
$router->get('/api/v1/entries/public', [$entryController, 'index']);
$router->get('/api/v1/entries/popular', [$entryController, 'index']);

// ===== 需要認證的路由 =====

// 用戶相關
$router->get('/api/v1/auth/me', [$authController, 'me'], [['AuthMiddleware', 'handle']]);
$router->put('/api/v1/auth/profile', [$authController, 'updateProfile'], [['AuthMiddleware', 'handle']]);
$router->put('/api/v1/auth/password', [$authController, 'changePassword'], [['AuthMiddleware', 'handle']]);

// 日記相關
$router->get('/api/v1/entries', [$entryController, 'index'], [['AuthMiddleware', 'handle']]);
$router->post('/api/v1/entries', [$entryController, 'create'], [['AuthMiddleware', 'handle']]);
$router->get('/api/v1/entries/{id}', [$entryController, 'show'], [['AuthMiddleware', 'optional']]);
$router->put('/api/v1/entries/{id}', [$entryController, 'update'], [['AuthMiddleware', 'handle']]);
$router->delete('/api/v1/entries/{id}', [$entryController, 'delete'], [['AuthMiddleware', 'handle']]);

// 搜索和統計
$router->get('/api/v1/entries/search', [$entryController, 'search'], [['AuthMiddleware', 'optional']]);
$router->get('/api/v1/stats', [$entryController, 'stats'], [['AuthMiddleware', 'handle']]);

// 測試數據庫連接
$router->get('/api/v1/test-db', function() {
    header('Content-Type: application/json');
    try {
        $db = Database::getInstance();
        $result = $db->fetchOne("SELECT COUNT(*) as count FROM users");
        echo json_encode([
            'success' => true,
            'message' => 'Database connection successful',
            'user_count' => $result['count']
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Database connection failed',
            'error' => $e->getMessage()
        ]);
    }
});

// 返回路由器實例
return $router;
