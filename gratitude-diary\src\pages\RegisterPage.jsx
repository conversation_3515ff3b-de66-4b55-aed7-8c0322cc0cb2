/**
 * 註冊頁面
 */

import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Divider,
  FormControlLabel,
  Checkbox
} from '@mui/material';
import { useForm } from 'react-hook-form';
import { useAuthContext } from '../contexts/AuthContext.jsx';

const RegisterPage = () => {
  const { register: registerUser, loading, error, clearError } = useAuthContext();
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError: setFormError,
    watch
  } = useForm();

  const password = watch('password');

  const onSubmit = async (data) => {
    clearError();

    const result = await registerUser({
      email: data.email,
      password: data.password,
      display_name: data.displayName,
      bio: data.bio || ''
    });

    if (result.success) {
      navigate('/', { replace: true });
    } else {
      setFormError('root', {
        type: 'manual',
        message: result.error
      });
    }
  };

  return (
    <Container component="main" maxWidth="sm">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Paper
          elevation={3}
          sx={{
            padding: 4,
            width: '100%',
            borderRadius: 2
          }}
        >
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <Typography component="h1" variant="h4" gutterBottom>
              感恩日記
            </Typography>
            <Typography variant="h6" color="text.secondary">
              創建新帳戶
            </Typography>
          </Box>

          {(error || errors.root) && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error || errors.root?.message}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
            <TextField
              margin="normal"
              required
              fullWidth
              id="displayName"
              label="顯示名稱"
              name="displayName"
              autoComplete="name"
              autoFocus
              {...register('displayName', {
                required: '請輸入顯示名稱',
                minLength: {
                  value: 2,
                  message: '顯示名稱至少需要2個字符'
                },
                maxLength: {
                  value: 50,
                  message: '顯示名稱不能超過50個字符'
                }
              })}
              error={!!errors.displayName}
              helperText={errors.displayName?.message}
            />

            <TextField
              margin="normal"
              required
              fullWidth
              id="email"
              label="電子郵件"
              name="email"
              autoComplete="email"
              {...register('email', {
                required: '請輸入電子郵件',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: '請輸入有效的電子郵件地址'
                }
              })}
              error={!!errors.email}
              helperText={errors.email?.message}
            />

            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="密碼"
              type={showPassword ? 'text' : 'password'}
              id="password"
              autoComplete="new-password"
              {...register('password', {
                required: '請輸入密碼',
                minLength: {
                  value: 6,
                  message: '密碼至少需要6個字符'
                },
                pattern: {
                  value: /^(?=.*[a-zA-Z])(?=.*\d)/,
                  message: '密碼必須包含至少一個字母和一個數字'
                }
              })}
              error={!!errors.password}
              helperText={errors.password?.message}
            />

            <TextField
              margin="normal"
              required
              fullWidth
              name="confirmPassword"
              label="確認密碼"
              type={showPassword ? 'text' : 'password'}
              id="confirmPassword"
              autoComplete="new-password"
              {...register('confirmPassword', {
                required: '請確認密碼',
                validate: value =>
                  value === password || '密碼不匹配'
              })}
              error={!!errors.confirmPassword}
              helperText={errors.confirmPassword?.message}
            />

            <TextField
              margin="normal"
              fullWidth
              id="bio"
              label="個人簡介 (選填)"
              name="bio"
              multiline
              rows={3}
              placeholder="簡單介紹一下自己..."
              {...register('bio', {
                maxLength: {
                  value: 200,
                  message: '個人簡介不能超過200個字符'
                }
              })}
              error={!!errors.bio}
              helperText={errors.bio?.message}
            />

            <FormControlLabel
              control={
                <Checkbox
                  {...register('agreeToTerms', {
                    required: '請同意服務條款'
                  })}
                  color="primary"
                />
              }
              label={
                <Typography variant="body2">
                  我同意{' '}
                  <Link
                    to="/terms"
                    style={{ color: 'inherit', textDecoration: 'underline' }}
                  >
                    服務條款
                  </Link>{' '}
                  和{' '}
                  <Link
                    to="/privacy"
                    style={{ color: 'inherit', textDecoration: 'underline' }}
                  >
                    隱私政策
                  </Link>
                </Typography>
              }
              sx={{ mt: 1 }}
            />
            {errors.agreeToTerms && (
              <Typography color="error" variant="caption" display="block">
                {errors.agreeToTerms.message}
              </Typography>
            )}

            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : '註冊'}
            </Button>

            <Divider sx={{ my: 2 }} />

            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2">
                已經有帳戶？{' '}
                <Link
                  to="/login"
                  style={{
                    color: 'inherit',
                    textDecoration: 'none',
                    fontWeight: 'bold'
                  }}
                >
                  立即登入
                </Link>
              </Typography>
            </Box>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default RegisterPage;
