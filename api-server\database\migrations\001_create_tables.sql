-- 感恩日記數據庫遷移腳本
-- 版本：1.0.0
-- 創建時間：2024-01-01

-- 創建數據庫
CREATE DATABASE IF NOT EXISTS gratitude_diary CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE gratitude_diary;

-- 用戶表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    uid VARCHAR(255) UNIQUE NOT NULL COMMENT 'Firebase UID或自定義ID',
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    avatar VARCHAR(500) NULL,
    bio TEXT NULL,
    language VARCHAR(10) DEFAULT 'zh-TW',
    timezone VARCHAR(50) DEFAULT 'Asia/Taipei',
    theme VARCHAR(20) DEFAULT 'light',
    
    -- 隱私設定
    profile_visible BOOLEAN DEFAULT TRUE,
    allow_comments BOOLEAN DEFAULT TRUE,
    allow_messages BOOLEAN DEFAULT FALSE,
    
    -- 通知設定
    daily_reminder BOOLEAN DEFAULT TRUE,
    focus_reminder BOOLEAN DEFAULT TRUE,
    social_interaction BOOLEAN DEFAULT TRUE,
    push_enabled BOOLEAN DEFAULT TRUE,
    
    -- 統計數據
    total_entries INT DEFAULT 0,
    total_likes INT DEFAULT 0,
    total_comments INT DEFAULT 0,
    focus_sessions INT DEFAULT 0,
    focus_minutes INT DEFAULT 0,
    streak_days INT DEFAULT 0,
    last_entry_date DATE NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_uid (uid),
    INDEX idx_created_at (created_at),
    INDEX idx_last_entry_date (last_entry_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 日記條目表
CREATE TABLE entries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content TEXT NOT NULL,
    emotion VARCHAR(50) NOT NULL,
    tags JSON NULL COMMENT '標籤數組',
    media JSON NULL COMMENT '媒體文件信息',
    privacy ENUM('public', 'friends', 'private') DEFAULT 'public',
    is_anonymous BOOLEAN DEFAULT FALSE,
    
    -- 位置信息
    latitude DECIMAL(10, 8) NULL,
    longitude DECIMAL(11, 8) NULL,
    address VARCHAR(500) NULL,
    
    -- 互動統計
    likes_count INT DEFAULT 0,
    comments_count INT DEFAULT 0,
    shares_count INT DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_emotion (emotion),
    INDEX idx_privacy (privacy),
    INDEX idx_created_at (created_at),
    INDEX idx_user_created (user_id, created_at),
    INDEX idx_privacy_emotion (privacy, emotion),
    INDEX idx_public_recent (privacy, created_at),
    FULLTEXT idx_content (content)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 互動記錄表
CREATE TABLE interactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    entry_id INT NOT NULL,
    type ENUM('like', 'comment', 'share') NOT NULL,
    content TEXT NULL COMMENT '評論內容',
    parent_id INT NULL COMMENT '回覆的評論ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (entry_id) REFERENCES entries(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES interactions(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_like (user_id, entry_id, type),
    INDEX idx_entry_type (entry_id, type),
    INDEX idx_user_type (user_id, type),
    INDEX idx_created_at (created_at),
    INDEX idx_parent_id (parent_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 專注工作記錄表
CREATE TABLE focus_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    work_duration INT NOT NULL COMMENT '工作時長(分鐘)',
    break_duration INT NOT NULL COMMENT '休息時長(分鐘)',
    actual_work_time INT NULL COMMENT '實際工作時間(分鐘)',
    gratitude_message TEXT NULL,
    is_completed BOOLEAN DEFAULT FALSE,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_start_time (start_time),
    INDEX idx_is_completed (is_completed),
    INDEX idx_user_completed (user_id, is_completed)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- FCM Token表
CREATE TABLE fcm_tokens (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    token VARCHAR(500) NOT NULL,
    platform ENUM('web', 'ios', 'android', 'desktop') NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_token (user_id, token),
    INDEX idx_user_active (user_id, is_active),
    INDEX idx_platform (platform),
    INDEX idx_token (token)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 通知記錄表
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    data JSON NULL COMMENT '額外數據',
    is_read BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP NULL,
    read_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_read (user_id, is_read),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at),
    INDEX idx_user_created (user_id, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 成就表
CREATE TABLE achievements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    icon VARCHAR(100) NOT NULL,
    condition_type VARCHAR(50) NOT NULL COMMENT '達成條件類型',
    condition_value INT NOT NULL COMMENT '達成條件數值',
    points INT DEFAULT 0 COMMENT '獎勵積分',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用戶成就表
CREATE TABLE user_achievements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    achievement_id INT NOT NULL,
    unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (achievement_id) REFERENCES achievements(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_achievement (user_id, achievement_id),
    INDEX idx_user_id (user_id),
    INDEX idx_unlocked_at (unlocked_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 系統配置表
CREATE TABLE system_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    description TEXT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 媒體文件表
CREATE TABLE media_files (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    entry_id INT NULL,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_type ENUM('image', 'audio', 'video') NOT NULL,
    thumbnail_path VARCHAR(500) NULL,
    duration INT NULL COMMENT '音頻/視頻時長(秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (entry_id) REFERENCES entries(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_entry_id (entry_id),
    INDEX idx_file_type (file_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 創建觸發器：更新用戶統計
DELIMITER $$

-- 新增日記時更新統計
CREATE TRIGGER update_user_stats_on_entry_insert
AFTER INSERT ON entries
FOR EACH ROW
BEGIN
    UPDATE users 
    SET total_entries = total_entries + 1,
        last_entry_date = CURDATE(),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.user_id;
END$$

-- 刪除日記時更新統計
CREATE TRIGGER update_user_stats_on_entry_delete
AFTER DELETE ON entries
FOR EACH ROW
BEGIN
    UPDATE users 
    SET total_entries = GREATEST(total_entries - 1, 0),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = OLD.user_id;
END$$

-- 新增點讚時更新統計
CREATE TRIGGER update_stats_on_like_insert
AFTER INSERT ON interactions
FOR EACH ROW
BEGIN
    IF NEW.type = 'like' THEN
        -- 更新日記點讚數
        UPDATE entries 
        SET likes_count = likes_count + 1 
        WHERE id = NEW.entry_id;
        
        -- 更新日記作者總點讚數
        UPDATE users 
        SET total_likes = total_likes + 1,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = (SELECT user_id FROM entries WHERE id = NEW.entry_id);
    ELSEIF NEW.type = 'comment' THEN
        -- 更新日記評論數
        UPDATE entries 
        SET comments_count = comments_count + 1 
        WHERE id = NEW.entry_id;
        
        -- 更新日記作者總評論數
        UPDATE users 
        SET total_comments = total_comments + 1,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = (SELECT user_id FROM entries WHERE id = NEW.entry_id);
    END IF;
END$$

-- 刪除互動時更新統計
CREATE TRIGGER update_stats_on_interaction_delete
AFTER DELETE ON interactions
FOR EACH ROW
BEGIN
    IF OLD.type = 'like' THEN
        -- 更新日記點讚數
        UPDATE entries 
        SET likes_count = GREATEST(likes_count - 1, 0) 
        WHERE id = OLD.entry_id;
        
        -- 更新日記作者總點讚數
        UPDATE users 
        SET total_likes = GREATEST(total_likes - 1, 0),
            updated_at = CURRENT_TIMESTAMP
        WHERE id = (SELECT user_id FROM entries WHERE id = OLD.entry_id);
    ELSEIF OLD.type = 'comment' THEN
        -- 更新日記評論數
        UPDATE entries 
        SET comments_count = GREATEST(comments_count - 1, 0) 
        WHERE id = OLD.entry_id;
        
        -- 更新日記作者總評論數
        UPDATE users 
        SET total_comments = GREATEST(total_comments - 1, 0),
            updated_at = CURRENT_TIMESTAMP
        WHERE id = (SELECT user_id FROM entries WHERE id = OLD.entry_id);
    END IF;
END$$

-- 完成專注工作時更新統計
CREATE TRIGGER update_focus_stats
AFTER UPDATE ON focus_sessions
FOR EACH ROW
BEGIN
    IF NEW.is_completed = TRUE AND OLD.is_completed = FALSE THEN
        UPDATE users 
        SET focus_sessions = focus_sessions + 1,
            focus_minutes = focus_minutes + COALESCE(NEW.actual_work_time, NEW.work_duration),
            updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.user_id;
    END IF;
END$$

DELIMITER ;

-- 創建視圖：用戶統計摘要
CREATE VIEW user_stats_summary AS
SELECT 
    u.id,
    u.display_name,
    u.total_entries,
    u.total_likes,
    u.total_comments,
    u.focus_sessions,
    u.focus_minutes,
    u.streak_days,
    u.last_entry_date,
    DATEDIFF(CURDATE(), u.last_entry_date) as days_since_last_entry,
    COUNT(DISTINCT ua.achievement_id) as total_achievements
FROM users u
LEFT JOIN user_achievements ua ON u.id = ua.user_id
GROUP BY u.id;

-- 創建視圖：熱門日記
CREATE VIEW popular_entries AS
SELECT 
    e.*,
    u.display_name,
    u.avatar,
    (e.likes_count * 3 + e.comments_count * 2 + e.shares_count) as popularity_score
FROM entries e
JOIN users u ON e.user_id = u.id
WHERE e.privacy = 'public'
ORDER BY popularity_score DESC, e.created_at DESC;

-- 創建索引優化查詢性能
CREATE INDEX idx_entries_popularity ON entries (privacy, likes_count, comments_count, created_at);
CREATE INDEX idx_users_active ON users (last_entry_date, created_at);
CREATE INDEX idx_interactions_recent ON interactions (created_at, type, entry_id);

-- 設置數據庫參數優化
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB
SET GLOBAL query_cache_size = 67108864; -- 64MB
SET GLOBAL query_cache_type = 1;
