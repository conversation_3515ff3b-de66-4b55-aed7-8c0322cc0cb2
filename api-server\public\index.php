<?php
require_once '../vendor/autoload.php';

// 載入環境變數
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// 設置錯誤報告
if ($_ENV['APP_DEBUG'] === 'true') {
    error_reporting(E_ALL);s
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// 設置時區
date_default_timezone_set('Asia/Taipei');

// CORS處理
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 載入服務
require_once '../app/Services/Database.php';
require_once '../app/Services/AuthService.php';

// 簡單的健康檢查端點
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$path = str_replace('/gratitude-diary-api/public', '', $path);

if ($path === '/health' || $path === '/api/v1/health') {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => 'API is running',
        'timestamp' => date('Y-m-d H:i:s'),
        'version' => '1.0.0'
    ]);
    exit();
}

// 測試數據庫連接
if ($path === '/test-db' || $path === '/api/v1/test-db') {
    header('Content-Type: application/json');
    try {
        $db = Database::getInstance();
        $result = $db->fetchOne("SELECT COUNT(*) as count FROM users");
        echo json_encode([
            'success' => true,
            'message' => 'Database connection successful',
            'user_count' => $result['count']
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Database connection failed',
            'error' => $e->getMessage()
        ]);
    }
    exit();
}

// 默認響應
header('Content-Type: application/json');
echo json_encode([
    'success' => true,
    'message' => 'Gratitude Diary API',
    'version' => '1.0.0',
    'endpoints' => [
        'health' => '/api/v1/health',
        'test-db' => '/api/v1/test-db'
    ]
]);