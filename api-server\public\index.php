<?php
/**
 * 感恩日記 API 入口文件
 * 版本：1.0.0
 */

// 設置 CORS headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept, Origin');

// 處理 OPTIONS 請求
if (($_SERVER['REQUEST_METHOD'] ?? '') === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 載入Composer自動加載器
require_once '../vendor/autoload.php';

use Dotenv\Dotenv;

try {
    // 載入環境變數
    $dotenv = Dotenv::createImmutable(__DIR__ . '/..');
    $dotenv->load();

    // 設置錯誤報告
    if (($_ENV['APP_DEBUG'] ?? 'false') === 'true') {
        error_reporting(E_ALL);
        ini_set('display_errors', 1);
    } else {
        error_reporting(0);
        ini_set('display_errors', 0);
    }

    // 設置時區
    date_default_timezone_set($_ENV['APP_TIMEZONE'] ?? 'Asia/Taipei');

    // 載入核心服務
    require_once '../app/Services/Database.php';
    require_once '../app/Services/AuthService.php';
    require_once '../app/Models/BaseModel.php';
    require_once '../app/Models/User.php';
    require_once '../app/Controllers/AuthController.php';

    // 初始化認證服務
    AuthService::init();

    // 獲取請求路徑和方法
    $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';

    // 檢查是否有 path 查詢參數
    if (isset($_GET['path'])) {
        $path = $_GET['path'];
    } else {
        $path = $_SERVER['REQUEST_URI'] ?? '/';
        $path = parse_url($path, PHP_URL_PATH);
    }

    // 簡單路由處理
    if ($path === '/api/v1/health' || $path === '/health' || $path === '/' ||
        strpos($path, '/index.php') !== false) {
        // 健康檢查
        $db = Database::getInstance();
        $result = $db->fetchOne("SELECT COUNT(*) as count FROM users");

        echo json_encode([
            'success' => true,
            'message' => 'API is running',
            'timestamp' => date('Y-m-d H:i:s'),
            'user_count' => $result['count'],
            'version' => '1.0.0'
        ]);

    } elseif ($path === '/api/v1/auth/register' && $method === 'POST') {
        // 用戶註冊
        $controller = new AuthController();
        $controller->register();

    } elseif ($path === '/api/v1/auth/login' && $method === 'POST') {
        // 用戶登入
        $controller = new AuthController();
        $controller->login();

    } elseif ($path === '/api/v1/auth/me' && $method === 'GET') {
        // 獲取當前用戶信息
        require_once '../app/Middleware/AuthMiddleware.php';
        if (AuthMiddleware::handle()) {
            $controller = new AuthController();
            $controller->me();
        }

    } elseif ($path === '/api/v1/auth/logout' && $method === 'POST') {
        // 用戶登出
        require_once '../app/Middleware/AuthMiddleware.php';
        if (AuthMiddleware::handle()) {
            $controller = new AuthController();
            $controller->logout();
        }

    } elseif ($path === '/api/v1/entries/public' && $method === 'GET') {
        // 獲取公開日記
        require_once '../app/Middleware/AuthMiddleware.php';
        require_once '../app/Models/Entry.php';
        require_once '../app/Controllers/EntryController.php';

        $controller = new EntryController();
        $_GET['type'] = 'public';
        $controller->index();

    } else {
        // 404 Not Found
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'Route not found',
            'message' => 'The requested endpoint does not exist',
            'path' => $path,
            'method' => $method,
            'debug' => [
                'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? 'not set',
                'GET_params' => $_GET,
                'POST_data' => file_get_contents('php://input')
            ]
        ]);
    }

} catch (Exception $e) {
    // 錯誤處理
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal Server Error',
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}