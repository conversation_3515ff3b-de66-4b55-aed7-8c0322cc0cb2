<?php
/**
 * 感恩日記 API 入口文件
 * 版本：1.0.0
 */

// 設置 CORS headers - 必須在任何輸出之前
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS, PATCH');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept, Origin, X-API-Key');
header('Access-Control-Allow-Credentials: false');
header('Access-Control-Max-Age: 86400'); // 24 hours

// 設置內容類型
if (strpos($_SERVER['REQUEST_URI'], '/docs') !== false) {
    header('Content-Type: text/html; charset=utf-8');
} elseif (strpos($_SERVER['REQUEST_URI'], '/swagger.json') !== false) {
    header('Content-Type: application/json; charset=utf-8');
} else {
    header('Content-Type: application/json; charset=utf-8');
}

// 處理 OPTIONS 請求
if (($_SERVER['REQUEST_METHOD'] ?? '') === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 載入Composer自動加載器
require_once '../vendor/autoload.php';

use Dotenv\Dotenv;

try {
    // 載入環境變數
    $dotenv = Dotenv::createImmutable(__DIR__ . '/..');
    $dotenv->load();

    // 設置錯誤報告
    if (($_ENV['APP_DEBUG'] ?? 'false') === 'true') {
        error_reporting(E_ALL);
        ini_set('display_errors', 1);
    } else {
        error_reporting(0);
        ini_set('display_errors', 0);
    }

    // 設置時區
    date_default_timezone_set($_ENV['APP_TIMEZONE'] ?? 'Asia/Taipei');

    // 載入核心服務
    require_once '../app/Services/Database.php';
    require_once '../app/Services/AuthService.php';
    require_once '../app/Models/BaseModel.php';
    require_once '../app/Models/User.php';
    require_once '../app/Controllers/AuthController.php';

    // 初始化認證服務
    AuthService::init();

    // 獲取請求路徑和方法
    $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';

    // 檢查是否有 path 查詢參數
    if (isset($_GET['path'])) {
        $path = $_GET['path'];
    } else {
        $path = $_SERVER['REQUEST_URI'] ?? '/';
        $path = parse_url($path, PHP_URL_PATH);
    }

    // 簡單路由處理
    if ($path === '/swagger.json' && $method === 'GET') {
        // 提供 Swagger JSON 文件
        header('Content-Type: application/json');
        $swaggerFile = '../swagger.json';
        if (file_exists($swaggerFile)) {
            echo file_get_contents($swaggerFile);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Swagger file not found']);
        }

    } elseif ($path === '/docs' && $method === 'GET') {
        // 提供 Swagger UI 頁面
        header('Content-Type: text/html');
        $swaggerUIFile = 'swagger-ui.html';
        if (file_exists($swaggerUIFile)) {
            echo file_get_contents($swaggerUIFile);
        } else {
            http_response_code(404);
            echo 'Swagger UI not found';
        }

    } elseif ($path === '/api/v1/health' || $path === '/health' || $path === '/' ||
        strpos($path, '/index.php') !== false) {
        // 健康檢查
        $db = Database::getInstance();
        $result = $db->fetchOne("SELECT COUNT(*) as count FROM users");

        echo json_encode([
            'success' => true,
            'message' => 'API is running',
            'timestamp' => date('Y-m-d H:i:s'),
            'user_count' => $result['count'],
            'version' => '1.0.0'
        ]);

    } elseif ($path === '/api/v1/auth/register' && $method === 'POST') {
        // 用戶註冊
        $controller = new AuthController();
        $controller->register();

    } elseif ($path === '/api/v1/auth/login' && $method === 'POST') {
        // 用戶登入
        $controller = new AuthController();
        $controller->login();

    } elseif ($path === '/api/v1/auth/me' && $method === 'GET') {
        // 獲取當前用戶信息
        require_once '../app/Middleware/AuthMiddleware.php';
        if (AuthMiddleware::handle()) {
            $controller = new AuthController();
            $controller->me();
        }

    } elseif ($path === '/api/v1/auth/logout' && $method === 'POST') {
        // 用戶登出
        require_once '../app/Middleware/AuthMiddleware.php';
        if (AuthMiddleware::handle()) {
            $controller = new AuthController();
            $controller->logout();
        }

    } elseif ($path === '/api/v1/entries/public' && $method === 'GET') {
        // 獲取公開日記
        require_once '../app/Middleware/AuthMiddleware.php';
        require_once '../app/Models/Entry.php';
        require_once '../app/Controllers/EntryController.php';

        $controller = new EntryController();
        $_GET['type'] = 'public';
        $controller->index();

    } elseif ($path === '/api/v1/entries' && $method === 'POST') {
        // 創建新日記
        require_once '../app/Middleware/AuthMiddleware.php';
        if (AuthMiddleware::handle()) {
            require_once '../app/Models/Entry.php';
            require_once '../app/Controllers/EntryController.php';

            $controller = new EntryController();
            $controller->create();
        }

    } elseif ($path === '/api/v1/entries' && $method === 'GET') {
        // 獲取用戶日記
        require_once '../app/Middleware/AuthMiddleware.php';
        if (AuthMiddleware::handle()) {
            require_once '../app/Models/Entry.php';
            require_once '../app/Controllers/EntryController.php';

            $controller = new EntryController();
            $controller->index();
        }

    } elseif (preg_match('/^\/api\/v1\/entries\/(\d+)$/', $path, $matches) && $method === 'GET') {
        // 獲取單篇日記
        require_once '../app/Middleware/AuthMiddleware.php';
        if (AuthMiddleware::handle()) {
            require_once '../app/Models/Entry.php';
            require_once '../app/Controllers/EntryController.php';

            $controller = new EntryController();
            $controller->show($matches[1]);
        }

    } elseif (preg_match('/^\/api\/v1\/entries\/(\d+)$/', $path, $matches) && $method === 'PUT') {
        // 更新日記
        require_once '../app/Middleware/AuthMiddleware.php';
        if (AuthMiddleware::handle()) {
            require_once '../app/Models/Entry.php';
            require_once '../app/Controllers/EntryController.php';

            $controller = new EntryController();
            $controller->update($matches[1]);
        }

    } elseif (preg_match('/^\/api\/v1\/entries\/(\d+)$/', $path, $matches) && $method === 'DELETE') {
        // 刪除日記
        require_once '../app/Middleware/AuthMiddleware.php';
        if (AuthMiddleware::handle()) {
            require_once '../app/Models/Entry.php';
            require_once '../app/Controllers/EntryController.php';

            $controller = new EntryController();
            $controller->delete($matches[1]);
        }

    } elseif ($path === '/api/v1/entries/upload' && $method === 'POST') {
        // 上傳圖片
        require_once '../app/Middleware/AuthMiddleware.php';
        if (AuthMiddleware::handle()) {
            require_once '../app/Models/Image.php';
            require_once '../app/Controllers/ImageController.php';

            $controller = new ImageController();
            $controller->upload();
        }

    } else {
        // 404 Not Found
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'Route not found',
            'message' => 'The requested endpoint does not exist',
            'path' => $path,
            'method' => $method,
            'debug' => [
                'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? 'not set',
                'GET_params' => $_GET,
                'POST_data' => file_get_contents('php://input')
            ]
        ]);
    }

} catch (Exception $e) {
    // 錯誤處理
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal Server Error',
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}