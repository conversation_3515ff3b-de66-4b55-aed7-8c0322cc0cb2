<?php
/**
 * 感恩日記 API 入口文件
 * 版本：1.0.0
 */

// 載入Composer自動加載器
require_once '../vendor/autoload.php';

use Dotenv\Dotenv;

// 載入環境變數
$dotenv = Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// 設置錯誤報告
if ($_ENV['APP_DEBUG'] === 'true') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// 設置時區
date_default_timezone_set($_ENV['APP_TIMEZONE'] ?? 'Asia/Taipei');

// 設置字符編碼
mb_internal_encoding('UTF-8');

// 載入核心服務
require_once '../app/Services/Database.php';
require_once '../app/Services/AuthService.php';

// 載入中間件
require_once '../app/Middleware/CorsMiddleware.php';
require_once '../app/Middleware/AuthMiddleware.php';

// 處理CORS
CorsMiddleware::handle();
CorsMiddleware::setSecurityHeaders();

// 載入路由
$router = require_once '../routes/api.php';

// 處理請求
try {
    $router->handleRequest();
} catch (Exception $e) {
    // 全局錯誤處理
    http_response_code(500);
    header('Content-Type: application/json');

    $response = [
        'success' => false,
        'error' => 'Internal Server Error'
    ];

    // 在開發環境顯示詳細錯誤
    if ($_ENV['APP_DEBUG'] === 'true') {
        $response['message'] = $e->getMessage();
        $response['trace'] = $e->getTraceAsString();
    }

    echo json_encode($response);

    // 記錄錯誤
    error_log('API Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());
}