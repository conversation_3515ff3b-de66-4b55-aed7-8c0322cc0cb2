import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  AppBar,
  Toolbar,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Switch,
  Card,
  Divider,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Alert,
  Chip,
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import VisibilityIcon from '@mui/icons-material/Visibility';
import SecurityIcon from '@mui/icons-material/Security';
import DataUsageIcon from '@mui/icons-material/DataUsage';
import DeleteIcon from '@mui/icons-material/Delete';
import DownloadIcon from '@mui/icons-material/Download';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import PersonIcon from '@mui/icons-material/Person';
import NotificationsIcon from '@mui/icons-material/Notifications';
import { motion } from 'framer-motion';

const PrivacySettingsPage = () => {
  const navigate = useNavigate();
  const [settings, setSettings] = useState({
    profileVisibility: true,
    shareAnalytics: false,
    locationTracking: false,
    personalizedAds: false,
    dataCollection: true,
    thirdPartySharing: false,
  });
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);

  const handleBack = () => {
    navigate('/app/settings');
  };

  const handleSettingChange = (key) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const handleDataExport = () => {
    setShowExportDialog(false);
    // 這裡實現數據導出邏輯
    console.log('開始導出用戶數據...');
  };

  const handleAccountDeletion = () => {
    setShowDeleteDialog(false);
    // 這裡實現帳戶刪除邏輯
    console.log('開始刪除帳戶...');
  };

  const privacySections = [
    {
      title: '隱私控制',
      items: [
        {
          icon: <VisibilityIcon />,
          title: '個人資料可見性',
          subtitle: '控制其他用戶是否能看到您的個人資料',
          key: 'profileVisibility',
          isSwitch: true,
        },
        {
          icon: <LocationOnIcon />,
          title: '位置追蹤',
          subtitle: '允許應用使用您的位置信息',
          key: 'locationTracking',
          isSwitch: true,
        },
        {
          icon: <PersonIcon />,
          title: '個性化廣告',
          subtitle: '根據您的使用習慣顯示相關廣告',
          key: 'personalizedAds',
          isSwitch: true,
        },
      ],
    },
    {
      title: '數據管理',
      items: [
        {
          icon: <DataUsageIcon />,
          title: '數據收集',
          subtitle: '允許收集使用數據以改善服務',
          key: 'dataCollection',
          isSwitch: true,
        },
        {
          icon: <SecurityIcon />,
          title: '第三方數據分享',
          subtitle: '與合作夥伴分享匿名化數據',
          key: 'thirdPartySharing',
          isSwitch: true,
        },
        {
          icon: <NotificationsIcon />,
          title: '分析數據分享',
          subtitle: '分享應用使用分析數據',
          key: 'shareAnalytics',
          isSwitch: true,
        },
      ],
    },
    {
      title: '數據權利',
      items: [
        {
          icon: <DownloadIcon />,
          title: '導出我的數據',
          subtitle: '下載您在應用中的所有數據',
          action: () => setShowExportDialog(true),
          showArrow: true,
        },
        {
          icon: <DeleteIcon />,
          title: '刪除我的帳戶',
          subtitle: '永久刪除帳戶和所有相關數據',
          action: () => setShowDeleteDialog(true),
          showArrow: true,
          isDestructive: true,
        },
      ],
    },
  ];

  return (
    <Box sx={{
      bgcolor: 'background.default',
      minHeight: '100vh',
      width: '100%',
      maxWidth: '100vw',
      margin: '0 auto',
    }}>
      {/* 頂部導航欄 */}
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar sx={{ maxWidth: '600px', width: '100%', margin: '0 auto' }}>
          <IconButton onClick={handleBack} sx={{ mr: 2 }}>
            <ArrowBackIcon sx={{ color: 'text.primary' }} />
          </IconButton>
          <Typography
            variant="h3"
            sx={{
              flex: 1,
              color: 'text.primary',
              fontWeight: 600,
            }}
          >
            隱私設定
          </Typography>
        </Toolbar>
      </AppBar>

      <Box sx={{
        p: 2,
        maxWidth: '600px',
        width: '100%',
        margin: '0 auto',
      }}>
        {/* 隱私說明 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Alert severity="info" sx={{ mb: 3, borderRadius: 3 }}>
            <Typography variant="body2">
              我們重視您的隱私。您可以隨時調整這些設定來控制您的數據如何被使用。
            </Typography>
          </Alert>
        </motion.div>

        {privacySections.map((section, sectionIndex) => (
          <motion.div
            key={section.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: sectionIndex * 0.1 + 0.1 }}
          >
            <Typography
              variant="h6"
              sx={{
                mb: 1,
                mt: sectionIndex > 0 ? 3 : 0,
                fontWeight: 600,
                color: 'text.secondary',
                fontSize: '0.875rem',
                textTransform: 'uppercase',
                letterSpacing: 0.5,
              }}
            >
              {section.title}
            </Typography>
            <Card sx={{ borderRadius: 3, mb: 2 }}>
              <List sx={{ py: 0 }}>
                {section.items.map((item, index) => (
                  <React.Fragment key={index}>
                    <ListItem
                      button={!item.isSwitch}
                      onClick={item.isSwitch ? undefined : item.action}
                      sx={{
                        py: 2,
                        '&:hover': {
                          bgcolor: item.isSwitch ? 'transparent' : 'action.hover',
                        },
                      }}
                    >
                      <ListItemIcon
                        sx={{
                          color: item.isDestructive ? 'error.main' : 'primary.main',
                          minWidth: 40,
                        }}
                      >
                        {item.icon}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography
                              variant="body1"
                              sx={{
                                fontWeight: 500,
                                color: item.isDestructive ? 'error.main' : 'text.primary',
                              }}
                            >
                              {item.title}
                            </Typography>
                            {item.key && !settings[item.key] && (
                              <Chip
                                label="已關閉"
                                size="small"
                                sx={{
                                  bgcolor: 'grey.100',
                                  color: 'grey.600',
                                  fontSize: '0.75rem',
                                }}
                              />
                            )}
                          </Box>
                        }
                        secondary={
                          <Typography variant="body2" color="text.secondary">
                            {item.subtitle}
                          </Typography>
                        }
                      />
                      {item.isSwitch ? (
                        <Switch
                          checked={settings[item.key]}
                          onChange={() => handleSettingChange(item.key)}
                          color="primary"
                        />
                      ) : null}
                    </ListItem>
                    {index < section.items.length - 1 && (
                      <Divider variant="inset" component="li" />
                    )}
                  </React.Fragment>
                ))}
              </List>
            </Card>
          </motion.div>
        ))}

        {/* 隱私政策連結 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <Box sx={{ textAlign: 'center', mt: 4, mb: 2 }}>
            <Typography variant="body2" color="text.secondary">
              了解更多關於我們如何保護您的隱私
            </Typography>
            <Button
              variant="text"
              color="primary"
              sx={{ mt: 1 }}
              onClick={() => console.log('查看隱私政策')}
            >
              查看隱私政策
            </Button>
          </Box>
        </motion.div>
      </Box>

      {/* 數據導出確認對話框 */}
      <Dialog
        open={showExportDialog}
        onClose={() => setShowExportDialog(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>導出數據</DialogTitle>
        <DialogContent>
          <Typography>
            我們將為您準備一個包含所有個人數據的文件。這可能需要幾分鐘時間，完成後會發送到您的註冊郵箱。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowExportDialog(false)}>
            取消
          </Button>
          <Button onClick={handleDataExport} color="primary" variant="contained">
            開始導出
          </Button>
        </DialogActions>
      </Dialog>

      {/* 刪除帳戶確認對話框 */}
      <Dialog
        open={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle sx={{ color: 'error.main' }}>刪除帳戶</DialogTitle>
        <DialogContent>
          <Typography sx={{ mb: 2 }}>
            ⚠️ 這個操作無法撤銷！刪除帳戶將會：
          </Typography>
          <Typography component="ul" sx={{ pl: 2 }}>
            <li>永久刪除所有日記記錄</li>
            <li>移除所有個人數據</li>
            <li>取消所有訂閱服務</li>
            <li>無法恢復任何信息</li>
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDeleteDialog(false)}>
            取消
          </Button>
          <Button onClick={handleAccountDeletion} color="error" variant="contained">
            確認刪除
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PrivacySettingsPage;
