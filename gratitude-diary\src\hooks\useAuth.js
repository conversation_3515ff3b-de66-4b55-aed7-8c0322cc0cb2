/**
 * 認證相關的 React Hook
 */

import { useState, useEffect, useCallback } from 'react';
import { authService } from '../services/authService.js';

export const useAuth = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 初始化認證狀態
  useEffect(() => {
    const initAuth = async () => {
      try {
        await authService.waitForInitialization();
        setUser(authService.getUser());
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    initAuth();

    // 監聽登出事件
    const handleLogout = () => {
      setUser(null);
    };

    window.addEventListener('auth:logout', handleLogout);

    return () => {
      window.removeEventListener('auth:logout', handleLogout);
    };
  }, []);

  // 登入
  const login = useCallback(async (credentials) => {
    setLoading(true);
    setError(null);

    try {
      const result = await authService.login(credentials);
      
      if (result.success) {
        setUser(result.user);
        return result;
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = err.message || '登入失敗';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  // 註冊
  const register = useCallback(async (userData) => {
    setLoading(true);
    setError(null);

    try {
      const result = await authService.register(userData);
      
      if (result.success) {
        setUser(result.user);
        return result;
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = err.message || '註冊失敗';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  // 登出
  const logout = useCallback(async () => {
    setLoading(true);
    
    try {
      await authService.logout();
      setUser(null);
      setError(null);
    } catch (err) {
      console.error('Logout error:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // 更新用戶資料
  const updateProfile = useCallback(async (profileData) => {
    setLoading(true);
    setError(null);

    try {
      const result = await authService.updateProfile(profileData);
      
      if (result.success) {
        setUser(result.user);
        return result;
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = err.message || '更新資料失敗';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  // 更改密碼
  const changePassword = useCallback(async (passwordData) => {
    setLoading(true);
    setError(null);

    try {
      const result = await authService.changePassword(passwordData);
      
      if (!result.success) {
        setError(result.error);
      }
      
      return result;
    } catch (err) {
      const errorMessage = err.message || '更改密碼失敗';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  // 刷新用戶信息
  const refreshUser = useCallback(async () => {
    if (!authService.isAuthenticated()) {
      return;
    }

    try {
      const result = await authService.getCurrentUser();
      if (result.success) {
        setUser(result.user);
      }
    } catch (err) {
      console.error('Refresh user error:', err);
      // 如果刷新失敗，可能是 token 過期，執行登出
      logout();
    }
  }, [logout]);

  return {
    // 狀態
    user,
    loading,
    error,
    isAuthenticated: !!user,
    
    // 方法
    login,
    register,
    logout,
    updateProfile,
    changePassword,
    refreshUser,
    
    // 工具方法
    clearError: () => setError(null),
    isAdmin: () => authService.isAdmin(),
    getUserId: () => authService.getUserId()
  };
};

// 檢查認證狀態的 Hook
export const useAuthStatus = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      await authService.waitForInitialization();
      setIsAuthenticated(authService.isAuthenticated());
      setLoading(false);
    };

    checkAuth();

    // 監聽登出事件
    const handleLogout = () => {
      setIsAuthenticated(false);
    };

    window.addEventListener('auth:logout', handleLogout);

    return () => {
      window.removeEventListener('auth:logout', handleLogout);
    };
  }, []);

  return { isAuthenticated, loading };
};

export default useAuth;
