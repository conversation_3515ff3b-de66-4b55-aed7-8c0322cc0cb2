import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  AppBar,
  Toolbar,
  IconButton,
  Card,
  CardContent,
  Tabs,
  Tab,
  Grid,
  Chip,
  Button,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Avatar,
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import ShareIcon from '@mui/icons-material/Share';
import FavoriteIcon from '@mui/icons-material/Favorite';
import ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';
import ImageIcon from '@mui/icons-material/Image';
import MicIcon from '@mui/icons-material/Mic';
import { motion } from 'framer-motion';

const MyDiariesPage = () => {
  const navigate = useNavigate();
  const [currentTab, setCurrentTab] = useState(0);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedDiary, setSelectedDiary] = useState(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const handleBack = () => {
    navigate('/app/profile');
  };

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const handleMenuOpen = (event, diary) => {
    setAnchorEl(event.currentTarget);
    setSelectedDiary(diary);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedDiary(null);
  };

  const handleDelete = () => {
    setShowDeleteDialog(true);
    handleMenuClose();
  };

  const confirmDelete = () => {
    console.log('刪除日記:', selectedDiary);
    setShowDeleteDialog(false);
    setSelectedDiary(null);
  };

  // 模擬用戶日記數據
  const myDiaries = [
    {
      id: 1,
      date: '2024-01-15',
      content: '今天陽光很溫暖，感謝大自然給予我們這樣美好的時刻。在公園裡散步時，看到孩子們開心地玩耍，讓我想起了童年的美好回憶。',
      emotion: 'gratitude',
      emotionLabel: '感恩',
      emotionColor: '#F59E0B',
      hasImage: true,
      hasAudio: false,
      likes: 12,
      comments: 3,
      isPublic: true,
      tags: ['#自然', '#童年', '#散步'],
    },
    {
      id: 2,
      date: '2024-01-14',
      content: '完成了一個重要的項目，感謝團隊的協作和自己的努力。每一步成長都值得慶祝！',
      emotion: 'growth',
      emotionLabel: '成長',
      emotionColor: '#6B46C1',
      hasImage: false,
      hasAudio: true,
      likes: 8,
      comments: 2,
      isPublic: true,
      tags: ['#工作', '#成長', '#團隊'],
    },
    {
      id: 3,
      date: '2024-01-13',
      content: '今天和家人一起吃晚餐，感受到家的溫暖。這些平凡的時刻其實是最珍貴的。',
      emotion: 'love',
      emotionLabel: '愛',
      emotionColor: '#C084FC',
      hasImage: true,
      hasAudio: false,
      likes: 15,
      comments: 5,
      isPublic: false,
      tags: ['#家庭', '#溫暖', '#晚餐'],
    },
    {
      id: 4,
      date: '2024-01-12',
      content: '早晨的第一縷陽光透過窗戶，帶來了新的希望和可能性。',
      emotion: 'hope',
      emotionLabel: '希望',
      emotionColor: '#10B981',
      hasImage: false,
      hasAudio: false,
      likes: 6,
      comments: 1,
      isPublic: true,
      tags: ['#早晨', '#陽光', '#希望'],
    },
  ];

  const getFilteredDiaries = () => {
    switch (currentTab) {
      case 0: return myDiaries; // 全部
      case 1: return myDiaries.filter(d => d.isPublic); // 公開
      case 2: return myDiaries.filter(d => !d.isPublic); // 私人
      default: return myDiaries;
    }
  };

  const DiaryCard = ({ diary, index }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
    >
      <Card
        sx={{
          mb: 2,
          borderRadius: 4,
          border: `2px solid ${diary.emotionColor}20`,
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: -1,
            left: -1,
            right: -1,
            bottom: -1,
            borderRadius: 4,
            background: `linear-gradient(45deg, ${diary.emotionColor}40, transparent)`,
            zIndex: -1,
          },
        }}
      >
        <CardContent sx={{ p: 2 }}>
          {/* 頭部信息 */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box sx={{ flex: 1 }}>
              <Typography variant="body2" fontWeight={600} color="primary.main">
                {new Date(diary.date).toLocaleDateString('zh-TW', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {diary.isPublic ? '公開分享' : '私人日記'}
              </Typography>
            </Box>
            {/* 媒體指示器 */}
            <Box sx={{ display: 'flex', gap: 0.5, mr: 1 }}>
              {diary.hasImage && (
                <ImageIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
              )}
              {diary.hasAudio && (
                <MicIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
              )}
            </Box>
            {/* 更多選項 */}
            <IconButton
              size="small"
              onClick={(e) => handleMenuOpen(e, diary)}
            >
              <MoreVertIcon />
            </IconButton>
          </Box>

          {/* 內容 */}
          <Typography
            variant="body2"
            sx={{
              mb: 2,
              lineHeight: 1.5,
              color: 'text.primary',
            }}
          >
            {diary.content}
          </Typography>

          {/* 標籤 */}
          <Box sx={{ display: 'flex', gap: 0.5, mb: 2, flexWrap: 'wrap' }}>
            {diary.tags.map((tag, index) => (
              <Chip
                key={index}
                label={tag}
                size="small"
                sx={{
                  bgcolor: `${diary.emotionColor}20`,
                  color: diary.emotionColor,
                  fontSize: '0.7rem',
                }}
              />
            ))}
          </Box>

          {/* 底部操作區 */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            {/* 情感標籤 */}
            <Chip
              label={diary.emotionLabel}
              size="small"
              sx={{
                bgcolor: `${diary.emotionColor}20`,
                color: diary.emotionColor,
                fontWeight: 600,
                fontSize: '0.75rem',
              }}
            />

            {/* 互動數據 */}
            {diary.isPublic && (
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <FavoriteIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                  <Typography variant="caption" color="text.secondary">
                    {diary.likes}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <ChatBubbleOutlineIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                  <Typography variant="caption" color="text.secondary">
                    {diary.comments}
                  </Typography>
                </Box>
              </Box>
            )}
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );

  return (
    <Box sx={{ 
      bgcolor: 'background.default', 
      minHeight: '100vh',
      width: '100%',
      maxWidth: '100vw',
      margin: '0 auto',
    }}>
      {/* 頂部導航欄 */}
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar sx={{ maxWidth: '800px', width: '100%', margin: '0 auto' }}>
          <IconButton onClick={handleBack} sx={{ mr: 2 }}>
            <ArrowBackIcon sx={{ color: 'text.primary' }} />
          </IconButton>
          <Typography
            variant="h3"
            sx={{
              flex: 1,
              color: 'text.primary',
              fontWeight: 600,
            }}
          >
            我的日記
          </Typography>
        </Toolbar>
      </AppBar>

      {/* 標籤頁 */}
      <Box sx={{ 
        borderBottom: 1, 
        borderColor: 'divider',
        bgcolor: 'background.paper',
      }}>
        <Box sx={{ maxWidth: '800px', width: '100%', margin: '0 auto' }}>
          <Tabs 
            value={currentTab} 
            onChange={handleTabChange}
            sx={{ px: 2 }}
          >
            <Tab label={`全部 (${myDiaries.length})`} />
            <Tab label={`公開 (${myDiaries.filter(d => d.isPublic).length})`} />
            <Tab label={`私人 (${myDiaries.filter(d => !d.isPublic).length})`} />
          </Tabs>
        </Box>
      </Box>

      {/* 日記列表 */}
      <Box sx={{ 
        p: 2,
        maxWidth: '800px',
        width: '100%',
        margin: '0 auto',
      }}>
        <Grid container spacing={1}>
          {getFilteredDiaries().map((diary, index) => (
            <Grid item xs={12} sm={6} key={diary.id}>
              <DiaryCard diary={diary} index={index} />
            </Grid>
          ))}
        </Grid>

        {getFilteredDiaries().length === 0 && (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
              還沒有日記記錄
            </Typography>
            <Button
              variant="contained"
              onClick={() => navigate('/app/create')}
              sx={{ px: 4 }}
            >
              開始記錄感恩
            </Button>
          </Box>
        )}
      </Box>

      {/* 更多選項菜單 */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleMenuClose}>
          <EditIcon sx={{ mr: 1 }} />
          編輯
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ShareIcon sx={{ mr: 1 }} />
          分享
        </MenuItem>
        <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1 }} />
          刪除
        </MenuItem>
      </Menu>

      {/* 刪除確認對話框 */}
      <Dialog
        open={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>確認刪除</DialogTitle>
        <DialogContent>
          <Typography>
            您確定要刪除這篇日記嗎？此操作無法撤銷。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDeleteDialog(false)}>
            取消
          </Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            刪除
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MyDiariesPage;
