# 感恩日記 - 數據庫設置指南

## 當前問題解決方案

### 問題1: composer.json 文件名錯誤
✅ **已解決** - 文件已重命名為正確的 `composer.json`

### 問題2: Composer 依賴安裝警告
✅ **已解決** - 依賴已成功安裝，警告不影響功能

### 問題3: 數據庫用戶不存在
❌ **需要手動解決** - 請按以下步驟操作

## 數據庫設置步驟

### 第一步：通過 phpMyAdmin 設置數據庫

1. 打開瀏覽器，訪問 `http://localhost/phpmyadmin`
2. 使用 root 用戶登入（通常密碼為空或 `root`）
3. 點擊 "SQL" 標籤
4. 複製並執行以下 SQL 命令：

```sql
-- 創建數據庫
CREATE DATABASE IF NOT EXISTS gratitude_diary CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 創建用戶
CREATE USER IF NOT EXISTS 'gratitude_user'@'localhost' IDENTIFIED BY 'admin!@#!@#';

-- 授予權限
GRANT ALL PRIVILEGES ON gratitude_diary.* TO 'gratitude_user'@'localhost';

-- 刷新權限
FLUSH PRIVILEGES;
```

### 第二步：導入數據庫結構

1. 在 phpMyAdmin 中選擇 `gratitude_diary` 數據庫
2. 點擊 "導入" 標籤
3. 選擇文件：`api-server/database/migrations/001_create_tables.sql`
4. 點擊 "執行"

### 第三步：導入初始數據

1. 確保仍在 `gratitude_diary` 數據庫中
2. 點擊 "導入" 標籤
3. 選擇文件：`api-server/database/seeds/initial_data.sql`
4. 點擊 "執行"

### 第四步：驗證設置

運行以下命令測試數據庫連接：

```powershell
cd api-server
php test_db.php
```

如果看到 "✅ 數據庫連接成功！" 和表格列表，說明設置成功。

## 替代方案：使用 WAMP MySQL 命令行

如果您熟悉命令行，也可以使用以下方式：

1. 找到 WAMP 的 MySQL bin 目錄（通常在 `C:\wamp64\bin\mysql\mysql8.x.x\bin\`）
2. 打開 PowerShell 並導航到該目錄
3. 執行以下命令：

```powershell
# 登入 MySQL
.\mysql.exe -u root -p

# 在 MySQL 命令行中執行
source E:\wamp8\www\diary\app\api-server\setup_database.sql
source E:\wamp8\www\diary\app\api-server\database\migrations\001_create_tables.sql
source E:\wamp8\www\diary\app\api-server\database\seeds\initial_data.sql
```

## 常見問題解決

### 問題：Access denied for user 'gratitude_user'
**解決方案**：確保已在 phpMyAdmin 中創建用戶並授予權限

### 問題：Table doesn't exist
**解決方案**：確保先導入 001_create_tables.sql，再導入 initial_data.sql

### 問題：Character set issues
**解決方案**：確保數據庫和表都使用 utf8mb4 字符集

## 下一步

數據庫設置完成後，您可以：

1. 測試 API 端點
2. 運行前端應用
3. 檢查功能是否正常工作

如有問題，請查看 `api-server/test_db.php` 的輸出以獲取詳細錯誤信息。
