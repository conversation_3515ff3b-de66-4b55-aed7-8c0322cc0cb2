<?php
require_once '../app/Services/Database.php';

try {
    $db = Database::getInstance();
    
    // 檢查用戶數量
    $userCount = $db->fetchOne("SELECT COUNT(*) as count FROM users");
    echo "用戶數量: " . $userCount['count'] . "\n";
    
    // 檢查日記數量
    $entryCount = $db->fetchOne("SELECT COUNT(*) as count FROM entries");
    echo "日記數量: " . $entryCount['count'] . "\n";
    
    // 檢查公開日記數量
    $publicEntryCount = $db->fetchOne("SELECT COUNT(*) as count FROM entries WHERE privacy = 'public'");
    echo "公開日記數量: " . $publicEntryCount['count'] . "\n";
    
    // 顯示所有日記
    $entries = $db->fetchAll("SELECT id, user_id, content, privacy, created_at FROM entries ORDER BY created_at DESC LIMIT 10");
    echo "\n最近的日記:\n";
    foreach ($entries as $entry) {
        echo "ID: {$entry['id']}, 用戶: {$entry['user_id']}, 隱私: {$entry['privacy']}, 內容: " . substr($entry['content'], 0, 50) . "...\n";
    }
    
} catch (Exception $e) {
    echo "錯誤: " . $e->getMessage() . "\n";
}
