-- 感恩日記初始數據
-- 版本：1.0.0

USE gratitude_diary;

-- 插入系統配置
INSERT INTO system_configs (config_key, config_value, description) VALUES
('app_version', '1.0.0', '應用版本號'),
('maintenance_mode', 'false', '維護模式開關'),
('max_entries_per_day', '10', '每日最大日記數量'),
('max_file_size', '10485760', '最大文件上傳大小(bytes)'),
('allowed_file_types', '["jpg","jpeg","png","gif","mp3","wav","m4a"]', '允許的文件類型'),
('daily_reminder_time', '20:00', '每日提醒時間'),
('focus_default_work_time', '25', '默認專注工作時間(分鐘)'),
('focus_default_break_time', '5', '默認休息時間(分鐘)'),
('streak_calculation_enabled', 'true', '是否啟用連續記錄計算'),
('public_entries_enabled', 'true', '是否啟用公開日記功能');

-- 插入成就數據
INSERT INTO achievements (code, name, description, icon, condition_type, condition_value, points) VALUES
-- 日記相關成就
('first_entry', '初心者', '發布第一篇日記', '🌱', 'total_entries', 1, 10),
('entries_10', '記錄者', '發布10篇日記', '📝', 'total_entries', 10, 50),
('entries_50', '日記達人', '發布50篇日記', '📚', 'total_entries', 50, 200),
('entries_100', '記錄大師', '發布100篇日記', '🏆', 'total_entries', 100, 500),
('entries_365', '年度記錄者', '發布365篇日記', '🎯', 'total_entries', 365, 1000),

-- 連續記錄成就
('streak_3', '三日堅持', '連續記錄3天', '🔥', 'streak_days', 3, 30),
('streak_7', '一週堅持', '連續記錄7天', '⭐', 'streak_days', 7, 70),
('streak_30', '月度堅持', '連續記錄30天', '💎', 'streak_days', 30, 300),
('streak_100', '百日堅持', '連續記錄100天', '👑', 'streak_days', 100, 1000),
('streak_365', '年度堅持', '連續記錄365天', '🌟', 'streak_days', 365, 3650),

-- 社交互動成就
('first_like', '點讚新手', '獲得第一個點讚', '👍', 'total_likes', 1, 5),
('likes_50', '受歡迎者', '獲得50個點讚', '❤️', 'total_likes', 50, 100),
('likes_200', '人氣王', '獲得200個點讚', '🌟', 'total_likes', 200, 400),
('comments_20', '互動達人', '獲得20條評論', '💬', 'total_comments', 20, 80),
('comments_100', '討論專家', '獲得100條評論', '🗣️', 'total_comments', 100, 300),

-- 專注工作成就
('first_focus', '專注新手', '完成第一次專注工作', '🎯', 'focus_sessions', 1, 10),
('focus_10', '專注者', '完成10次專注工作', '⏰', 'focus_sessions', 10, 50),
('focus_50', '專注達人', '完成50次專注工作', '🧘', 'focus_sessions', 50, 200),
('focus_hours_10', '十小時專注', '累計專注10小時', '⏳', 'focus_minutes', 600, 100),
('focus_hours_50', '五十小時專注', '累計專注50小時', '🕐', 'focus_minutes', 3000, 500),

-- 情感多樣性成就
('emotion_explorer', '情感探索者', '使用5種不同情感標籤', 'emotion_variety', 5, 50),
('emotion_master', '情感大師', '使用10種不同情感標籤', 'emotion_variety', 10, 150),

-- 特殊成就
('early_bird', '早起鳥', '在早上6點前發布日記', 'early_entry', 1, 20),
('night_owl', '夜貓子', '在晚上11點後發布日記', 'late_entry', 1, 20),
('weekend_warrior', '週末戰士', '週末連續記錄', 'weekend_streak', 2, 30),
('gratitude_master', '感恩大師', '發布100篇感恩類型日記', 'gratitude_entries', 100, 300);

-- 插入測試用戶（密碼：password123）
INSERT INTO users (uid, email, password, display_name, bio) VALUES 
('test_user_001', '<EMAIL>', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukj/LkLjK', '測試用戶', '這是一個測試用戶帳戶'),
('demo_user_001', '<EMAIL>', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukj/LkLjK', '演示用戶', '用於演示的用戶帳戶'),
('admin_user_001', '<EMAIL>', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukj/LkLjK', '管理員', '系統管理員帳戶');

-- 插入示例日記
INSERT INTO entries (user_id, content, emotion, tags, privacy, created_at) VALUES 
(1, '今天是美好的一天！感謝生活中的每一個小確幸。早上的陽光特別溫暖，讓我想起了童年的美好時光。', 'gratitude', '["生活", "陽光", "童年"]', 'public', '2024-01-01 09:30:00'),
(1, '工作雖然忙碌，但團隊合作讓我感到很開心。感謝同事們的支持和幫助。', 'joy', '["工作", "團隊", "合作"]', 'public', '2024-01-02 18:45:00'),
(1, '今天學會了一個新技能，內心充滿了成就感。學習真的是一件快樂的事情。', 'achievement', '["學習", "技能", "成長"]', 'public', '2024-01-03 20:15:00'),
(2, '和家人一起度過了溫馨的晚餐時光，感謝家人的陪伴和關愛。', 'love', '["家人", "晚餐", "溫馨"]', 'public', '2024-01-01 19:30:00'),
(2, '今天的冥想讓我內心平靜，感謝這份寧靜的時光。', 'peace', '["冥想", "平靜", "寧靜"]', 'public', '2024-01-02 07:00:00'),
(2, '看到朋友取得成功，為他感到高興。友誼真的很珍貴。', 'happiness', '["朋友", "成功", "友誼"]', 'friends', '2024-01-03 16:20:00');

-- 插入示例互動
INSERT INTO interactions (user_id, entry_id, type, created_at) VALUES 
(2, 1, 'like', '2024-01-01 10:00:00'),
(2, 2, 'like', '2024-01-02 19:00:00'),
(1, 4, 'like', '2024-01-01 20:00:00'),
(1, 5, 'like', '2024-01-02 08:00:00');

INSERT INTO interactions (user_id, entry_id, type, content, created_at) VALUES 
(2, 1, 'comment', '很棒的分享！我也喜歡早晨的陽光。', '2024-01-01 11:00:00'),
(1, 4, 'comment', '家人的陪伴確實很珍貴，感謝分享。', '2024-01-01 21:00:00'),
(2, 3, 'comment', '學習新技能的感覺真的很棒！', '2024-01-03 21:00:00');

-- 插入示例專注工作記錄
INSERT INTO focus_sessions (user_id, work_duration, break_duration, actual_work_time, gratitude_message, is_completed, start_time, end_time, created_at) VALUES 
(1, 25, 5, 25, '感謝這段專注的工作時光，讓我完成了重要的任務。', TRUE, '2024-01-01 14:00:00', '2024-01-01 14:25:00', '2024-01-01 14:25:00'),
(1, 25, 5, 23, '雖然沒有完全專注，但還是有所收穫。', TRUE, '2024-01-02 10:00:00', '2024-01-02 10:23:00', '2024-01-02 10:23:00'),
(2, 30, 10, 30, '感謝專注的力量，讓我能夠深入思考問題。', TRUE, '2024-01-01 15:00:00', '2024-01-01 15:30:00', '2024-01-01 15:30:00'),
(2, 25, 5, 25, '專注工作讓我感到充實和滿足。', TRUE, '2024-01-02 09:00:00', '2024-01-02 09:25:00', '2024-01-02 09:25:00');

-- 插入示例FCM Token
INSERT INTO fcm_tokens (user_id, token, platform, is_active) VALUES 
(1, 'test_fcm_token_web_user1', 'web', TRUE),
(1, 'test_fcm_token_android_user1', 'android', TRUE),
(2, 'test_fcm_token_web_user2', 'web', TRUE),
(2, 'test_fcm_token_ios_user2', 'ios', TRUE);

-- 插入示例通知
INSERT INTO notifications (user_id, type, title, content, data, is_read, sent_at, created_at) VALUES 
(1, 'like', '有人點讚了你的日記', '測試用戶點讚了你的日記「今天是美好的一天！」', '{"entry_id": 1, "from_user_id": 2}', FALSE, '2024-01-01 10:00:00', '2024-01-01 10:00:00'),
(1, 'comment', '有人評論了你的日記', '測試用戶評論了你的日記「今天是美好的一天！」', '{"entry_id": 1, "from_user_id": 2, "comment_id": 1}', FALSE, '2024-01-01 11:00:00', '2024-01-01 11:00:00'),
(2, 'like', '有人點讚了你的日記', '測試用戶點讚了你的日記「和家人一起度過了溫馨的晚餐時光」', '{"entry_id": 4, "from_user_id": 1}', TRUE, '2024-01-01 20:00:00', '2024-01-01 20:00:00'),
(1, 'daily_reminder', '每日感恩提醒', '記錄今天讓你感恩的美好時刻吧！', '{"type": "daily_reminder"}', FALSE, '2024-01-04 20:00:00', '2024-01-04 20:00:00'),
(2, 'focus_reminder', '專注工作提醒', '是時候開始一段專注的工作時光了！', '{"type": "focus_reminder"}', FALSE, '2024-01-04 09:00:00', '2024-01-04 09:00:00');

-- 插入用戶成就
INSERT INTO user_achievements (user_id, achievement_id, unlocked_at) VALUES 
(1, 1, '2024-01-01 09:30:00'), -- 初心者
(1, 6, '2024-01-03 20:15:00'), -- 三日堅持
(1, 16, '2024-01-01 10:00:00'), -- 點讚新手
(1, 21, '2024-01-01 14:25:00'), -- 專注新手
(2, 1, '2024-01-01 19:30:00'), -- 初心者
(2, 6, '2024-01-03 16:20:00'), -- 三日堅持
(2, 16, '2024-01-01 20:00:00'), -- 點讚新手
(2, 21, '2024-01-01 15:30:00'); -- 專注新手

-- 更新用戶統計數據（觸發器會自動更新，這裡是為了確保數據一致性）
UPDATE users SET 
    total_entries = (SELECT COUNT(*) FROM entries WHERE user_id = users.id),
    total_likes = (SELECT COUNT(*) FROM interactions i JOIN entries e ON i.entry_id = e.id WHERE e.user_id = users.id AND i.type = 'like'),
    total_comments = (SELECT COUNT(*) FROM interactions i JOIN entries e ON i.entry_id = e.id WHERE e.user_id = users.id AND i.type = 'comment'),
    focus_sessions = (SELECT COUNT(*) FROM focus_sessions WHERE user_id = users.id AND is_completed = TRUE),
    focus_minutes = (SELECT COALESCE(SUM(actual_work_time), 0) FROM focus_sessions WHERE user_id = users.id AND is_completed = TRUE),
    last_entry_date = (SELECT DATE(MAX(created_at)) FROM entries WHERE user_id = users.id),
    streak_days = 3, -- 示例數據，實際應該通過計算得出
    updated_at = CURRENT_TIMESTAMP;

-- 創建存儲過程：計算用戶連續記錄天數
DELIMITER $$

CREATE PROCEDURE CalculateUserStreak(IN userId INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE currentDate DATE;
    DECLARE streakCount INT DEFAULT 0;
    DECLARE maxStreak INT DEFAULT 0;
    DECLARE prevDate DATE DEFAULT NULL;
    
    DECLARE date_cursor CURSOR FOR 
        SELECT DISTINCT DATE(created_at) as entry_date 
        FROM entries 
        WHERE user_id = userId 
        ORDER BY entry_date DESC;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN date_cursor;
    
    read_loop: LOOP
        FETCH date_cursor INTO currentDate;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        IF prevDate IS NULL THEN
            SET streakCount = 1;
            SET prevDate = currentDate;
        ELSEIF DATEDIFF(prevDate, currentDate) = 1 THEN
            SET streakCount = streakCount + 1;
            SET prevDate = currentDate;
        ELSE
            IF streakCount > maxStreak THEN
                SET maxStreak = streakCount;
            END IF;
            SET streakCount = 1;
            SET prevDate = currentDate;
        END IF;
    END LOOP;
    
    IF streakCount > maxStreak THEN
        SET maxStreak = streakCount;
    END IF;
    
    CLOSE date_cursor;
    
    -- 檢查是否包含今天，如果不包含則streak為0
    IF NOT EXISTS (SELECT 1 FROM entries WHERE user_id = userId AND DATE(created_at) = CURDATE()) THEN
        IF NOT EXISTS (SELECT 1 FROM entries WHERE user_id = userId AND DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)) THEN
            SET maxStreak = 0;
        END IF;
    END IF;
    
    UPDATE users SET streak_days = maxStreak WHERE id = userId;
END$$

DELIMITER ;

-- 為示例用戶計算連續記錄天數
CALL CalculateUserStreak(1);
CALL CalculateUserStreak(2);

-- 創建定時任務相關的事件（需要開啟事件調度器）
-- SET GLOBAL event_scheduler = ON;

-- 每日凌晨更新所有用戶的連續記錄天數
-- CREATE EVENT IF NOT EXISTS update_daily_streaks
-- ON SCHEDULE EVERY 1 DAY
-- STARTS '2024-01-01 01:00:00'
-- DO
-- BEGIN
--     DECLARE done INT DEFAULT FALSE;
--     DECLARE userId INT;
--     DECLARE user_cursor CURSOR FOR SELECT id FROM users;
--     DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
--     
--     OPEN user_cursor;
--     
--     read_loop: LOOP
--         FETCH user_cursor INTO userId;
--         IF done THEN
--             LEAVE read_loop;
--         END IF;
--         
--         CALL CalculateUserStreak(userId);
--     END LOOP;
--     
--     CLOSE user_cursor;
-- END;

-- 插入一些常用的情感標籤數據（用於前端選擇）
INSERT INTO system_configs (config_key, config_value, description) VALUES
('emotion_types', '["gratitude","joy","peace","love","hope","excitement","contentment","pride","relief","inspiration","wonder","serenity","compassion","optimism","fulfillment"]', '可用的情感類型列表'),
('popular_tags', '["生活","工作","家庭","朋友","學習","健康","旅行","美食","運動","音樂","閱讀","電影","自然","寵物","成長"]', '熱門標籤列表'),
('gratitude_prompts', '["今天讓我感恩的三件事是...","我想感謝的人是...","今天的小確幸是...","讓我感到溫暖的時刻...","我珍惜的回憶是..."]', '感恩提示語列表');

-- 創建數據統計視圖
CREATE VIEW daily_stats AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_entries,
    COUNT(DISTINCT user_id) as active_users,
    AVG(CHAR_LENGTH(content)) as avg_content_length
FROM entries 
GROUP BY DATE(created_at)
ORDER BY date DESC;

CREATE VIEW emotion_stats AS
SELECT 
    emotion,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM entries), 2) as percentage
FROM entries 
GROUP BY emotion 
ORDER BY count DESC;

-- 確保所有表都使用正確的字符集
ALTER TABLE users CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE entries CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE interactions CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE focus_sessions CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE fcm_tokens CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE notifications CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE achievements CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE user_achievements CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE system_configs CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE media_files CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
