/**
 * API 客戶端配置
 * 使用 fetch API 進行 HTTP 請求
 */

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';

/**
 * API 客戶端類
 */
class ApiClient {
  constructor() {
    this.baseURL = API_BASE_URL;
    this.token = localStorage.getItem('auth_token');
  }

  /**
   * 設置認證 token
   */
  setToken(token) {
    this.token = token;
    if (token) {
      localStorage.setItem('auth_token', token);
    } else {
      localStorage.removeItem('auth_token');
    }
  }

  /**
   * 獲取認證 token
   */
  getToken() {
    return this.token || localStorage.getItem('auth_token');
  }

  /**
   * 構建請求 headers
   */
  getHeaders(customHeaders = {}) {
    const headers = {
      'Content-Type': 'application/json',
      ...customHeaders
    };

    const token = this.getToken();
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    return headers;
  }

  /**
   * 處理響應
   */
  async handleResponse(response) {
    const contentType = response.headers.get('content-type');
    
    let data;
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    if (!response.ok) {
      const error = new Error(data.error || data.message || `HTTP ${response.status}`);
      error.status = response.status;
      error.data = data;
      throw error;
    }

    return data;
  }

  /**
   * 通用請求方法
   */
  async request(endpoint, options = {}) {
    // 如果 baseURL 已經包含 index.php，則使用查詢參數
    let url;
    if (this.baseURL.includes('index.php')) {
      // 構建查詢參數
      const searchParams = new URLSearchParams();
      searchParams.set('path', endpoint);

      // 如果是 GET 請求且有額外參數，添加到查詢字符串
      if (options.method === 'GET' && options.params) {
        Object.keys(options.params).forEach(key => {
          if (options.params[key] !== undefined && options.params[key] !== null) {
            searchParams.set(key, options.params[key]);
          }
        });
      }

      url = `${this.baseURL}?${searchParams.toString()}`;
    } else {
      url = `${this.baseURL}${endpoint}`;
    }

    const config = {
      headers: this.getHeaders(options.headers),
      method: options.method || 'GET',
      body: options.body
    };

    try {
      const response = await fetch(url, config);
      return await this.handleResponse(response);
    } catch (error) {
      // 處理網絡錯誤
      if (error.status === 401) {
        // 認證失敗，清除 token
        this.setToken(null);
        // 可以在這裡觸發登出邏輯
        window.dispatchEvent(new CustomEvent('auth:logout'));
      }
      
      console.error('API Request Error:', error);
      throw error;
    }
  }

  /**
   * GET 請求
   */
  async get(endpoint, params = {}) {
    // 如果 baseURL 包含 index.php，使用查詢參數模式
    if (this.baseURL.includes('index.php')) {
      // 直接使用 endpoint，不要雙重編碼
      return this.request(endpoint, {
        method: 'GET',
        params: params
      });
    } else {
      // 標準 REST API 模式
      const url = new URL(`${this.baseURL}${endpoint}`);
      Object.keys(params).forEach(key => {
        if (params[key] !== undefined && params[key] !== null) {
          url.searchParams.append(key, params[key]);
        }
      });

      return this.request(url.pathname + url.search, {
        method: 'GET'
      });
    }
  }

  /**
   * POST 請求
   */
  async post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  /**
   * PUT 請求
   */
  async put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  /**
   * DELETE 請求
   */
  async delete(endpoint) {
    return this.request(endpoint, {
      method: 'DELETE'
    });
  }

  /**
   * 文件上傳
   */
  async upload(endpoint, formData) {
    return this.request(endpoint, {
      method: 'POST',
      headers: {
        // 不設置 Content-Type，讓瀏覽器自動設置
        Authorization: this.getToken() ? `Bearer ${this.getToken()}` : undefined
      },
      body: formData
    });
  }

  /**
   * 健康檢查
   */
  async healthCheck() {
    // 直接調用根路徑的健康檢查
    return this.request('/health', { method: 'GET' });
  }
}

// 創建全局 API 客戶端實例
export const apiClient = new ApiClient();

// 導出 API 基礎 URL
export { API_BASE_URL };

// 默認導出
export default apiClient;
