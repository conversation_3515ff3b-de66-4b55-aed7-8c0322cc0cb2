<?php
/**
 * CORS中間件
 * 處理跨域請求
 */
class CorsMiddleware {
    
    /**
     * 處理CORS
     */
    public static function handle() {
        // 允許的來源
        $allowedOrigins = [
            'http://localhost:3000',
            'http://localhost:5173',
            'http://localhost:8080',
            'https://gratitude-diary.com',
            'https://www.gratitude-diary.com'
        ];
        
        // 獲取請求來源
        $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
        
        // 檢查來源是否被允許
        if (in_array($origin, $allowedOrigins) || self::isDevelopment()) {
            header("Access-Control-Allow-Origin: $origin");
        }
        
        // 設置CORS headers
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS, PATCH');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept, Origin');
        header('Access-Control-Max-Age: 86400'); // 24小時
        
        // 處理預檢請求
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit();
        }
    }
    
    /**
     * 檢查是否為開發環境
     */
    private static function isDevelopment() {
        $env = $_ENV['APP_ENV'] ?? 'production';
        return $env === 'development' || $env === 'local';
    }
    
    /**
     * 設置安全headers
     */
    public static function setSecurityHeaders() {
        // 防止XSS攻擊
        header('X-XSS-Protection: 1; mode=block');
        
        // 防止MIME類型嗅探
        header('X-Content-Type-Options: nosniff');
        
        // 防止點擊劫持
        header('X-Frame-Options: DENY');
        
        // 強制HTTPS（生產環境）
        if (!self::isDevelopment()) {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
        }
        
        // 內容安全策略
        header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:;");
        
        // 推薦者策略
        header('Referrer-Policy: strict-origin-when-cross-origin');
    }
}
