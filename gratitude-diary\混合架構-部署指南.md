# 混合架構 - 部署指南

## 概述

本指南詳細說明如何部署混合架構的感恩日記應用：
- **後端**: PHP API + MySQL
- **推播**: Firebase Cloud Messaging
- **前端**: React Web應用
- **部署**: 傳統LAMP/LEMP架構

## 部署架構圖

```
┌─────────────────────────────────────────────────────────┐
│                    CDN/反向代理                          │
│                 (Cloudflare/Nginx)                     │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                   Web服務器                              │
│              (Apache/Nginx + PHP)                      │
├─────────────────┬───────────────────────────────────────┤
│   React前端     │           PHP API                     │
│   (靜態文件)     │      (/api/v1/...)                   │
└─────────────────┴───────────────────────────────────────┘
                              │
┌─────────────────┬─────────────────────────┬─────────────┐
│   MySQL數據庫   │   Firebase FCM          │   文件存儲   │
│                │   (推播服務)             │             │
└─────────────────┴─────────────────────────┴─────────────┘
```

## 服務器環境準備

### 1. 系統要求
```bash
# Ubuntu 22.04 LTS (推薦)
# 最低配置：2核CPU, 4GB RAM, 40GB SSD
# 推薦配置：4核CPU, 8GB RAM, 100GB SSD

# 更新系統
sudo apt update && sudo apt upgrade -y

# 安裝基本工具
sudo apt install -y curl wget git unzip software-properties-common
```

### 2. 安裝LAMP環境
```bash
# 安裝Apache
sudo apt install -y apache2

# 安裝MySQL
sudo apt install -y mysql-server
sudo mysql_secure_installation

# 安裝PHP 8.2
sudo add-apt-repository ppa:ondrej/php
sudo apt update
sudo apt install -y php8.2 php8.2-fpm php8.2-mysql php8.2-curl php8.2-gd php8.2-mbstring php8.2-xml php8.2-zip php8.2-json php8.2-bcmath

# 安裝Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# 啟用Apache模組
sudo a2enmod rewrite
sudo a2enmod ssl
sudo a2enmod headers
sudo systemctl restart apache2
```

### 3. 配置MySQL
```sql
-- 創建數據庫和用戶
CREATE DATABASE gratitude_diary CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'gratitude_user'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON gratitude_diary.* TO 'gratitude_user'@'localhost';
FLUSH PRIVILEGES;

-- 優化MySQL配置
-- 編輯 /etc/mysql/mysql.conf.d/mysqld.cnf
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
max_connections = 200
query_cache_size = 64M
query_cache_type = 1
```

## 後端API部署

### 1. 部署PHP API
```bash
# 創建專案目錄
sudo mkdir -p /var/www/gratitude-diary-api
sudo chown -R $USER:www-data /var/www/gratitude-diary-api

# 克隆或上傳代碼
cd /var/www/gratitude-diary-api
git clone <your-api-repo> .

# 安裝依賴
composer install --no-dev --optimize-autoloader

# 設置權限
sudo chown -R www-data:www-data storage/
sudo chmod -R 775 storage/
sudo chmod -R 755 public/
```

### 2. 環境配置
```bash
# 複製環境配置
cp .env.example .env

# 編輯環境變數
nano .env
```

```bash
# .env (生產環境)
APP_NAME="Gratitude Diary API"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://api.your-domain.com

# 數據庫配置
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=gratitude_diary
DB_USERNAME=gratitude_user
DB_PASSWORD=secure_password_here

# Firebase配置
FIREBASE_PROJECT_ID=your-project-id
FCM_SERVER_KEY=your-fcm-server-key
FCM_SENDER_ID=your-sender-id
FIREBASE_API_KEY=your-api-key

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-min-32-chars
JWT_EXPIRE=86400

# 文件上傳配置
UPLOAD_PATH=storage/uploads
MAX_FILE_SIZE=10485760
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,mp3,wav,m4a

# 郵件配置
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Gratitude Diary"

# 安全配置
CORS_ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com
RATE_LIMIT_PER_MINUTE=100
```

### 3. Apache虛擬主機配置
```apache
# /etc/apache2/sites-available/gratitude-diary-api.conf
<VirtualHost *:80>
    ServerName api.your-domain.com
    DocumentRoot /var/www/gratitude-diary-api/public
    
    # 重定向到HTTPS
    Redirect permanent / https://api.your-domain.com/
</VirtualHost>

<VirtualHost *:443>
    ServerName api.your-domain.com
    DocumentRoot /var/www/gratitude-diary-api/public
    
    # SSL配置
    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/your-domain.crt
    SSLCertificateKeyFile /etc/ssl/private/your-domain.key
    
    # PHP配置
    <FilesMatch \.php$>
        SetHandler "proxy:unix:/var/run/php/php8.2-fpm.sock|fcgi://localhost"
    </FilesMatch>
    
    # 目錄配置
    <Directory /var/www/gratitude-diary-api/public>
        AllowOverride All
        Require all granted
        
        # 安全標頭
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
        Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
        
        # CORS配置
        Header always set Access-Control-Allow-Origin "https://your-domain.com"
        Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
        Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
    </Directory>
    
    # 隱藏敏感文件
    <Files ".env">
        Require all denied
    </Files>
    
    <Files "composer.json">
        Require all denied
    </Files>
    
    # 日誌配置
    ErrorLog ${APACHE_LOG_DIR}/gratitude-diary-api_error.log
    CustomLog ${APACHE_LOG_DIR}/gratitude-diary-api_access.log combined
</VirtualHost>
```

### 4. 啟用站點
```bash
# 啟用站點
sudo a2ensite gratitude-diary-api.conf
sudo systemctl reload apache2

# 測試配置
sudo apache2ctl configtest
```

## 數據庫初始化

### 1. 運行遷移腳本
```bash
# 執行數據庫遷移
cd /var/www/gratitude-diary-api
mysql -u gratitude_user -p gratitude_diary < database/migrations/001_create_tables.sql

# 插入初始數據
mysql -u gratitude_user -p gratitude_diary < database/seeds/initial_data.sql
```

### 2. 數據庫備份腳本
```bash
#!/bin/bash
# scripts/backup-database.sh

DB_NAME="gratitude_diary"
DB_USER="gratitude_user"
DB_PASS="secure_password_here"
BACKUP_DIR="/var/backups/mysql"
DATE=$(date +%Y%m%d_%H%M%S)

# 創建備份目錄
mkdir -p $BACKUP_DIR

# 執行備份
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/gratitude_diary_$DATE.sql

# 壓縮備份文件
gzip $BACKUP_DIR/gratitude_diary_$DATE.sql

# 刪除7天前的備份
find $BACKUP_DIR -name "gratitude_diary_*.sql.gz" -mtime +7 -delete

echo "數據庫備份完成: gratitude_diary_$DATE.sql.gz"
```

```bash
# 設置定時備份
sudo crontab -e

# 每天凌晨2點備份
0 2 * * * /var/www/gratitude-diary-api/scripts/backup-database.sh
```

## 前端部署

### 1. 建置React應用
```bash
# 在本地或CI/CD環境建置
npm install
npm run build:production

# 上傳建置文件到服務器
rsync -avz --delete dist/ user@your-server:/var/www/gratitude-diary-web/
```

### 2. 前端Apache配置
```apache
# /etc/apache2/sites-available/gratitude-diary-web.conf
<VirtualHost *:80>
    ServerName your-domain.com
    ServerAlias www.your-domain.com
    DocumentRoot /var/www/gratitude-diary-web
    
    # 重定向到HTTPS
    Redirect permanent / https://your-domain.com/
</VirtualHost>

<VirtualHost *:443>
    ServerName your-domain.com
    ServerAlias www.your-domain.com
    DocumentRoot /var/www/gratitude-diary-web
    
    # SSL配置
    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/your-domain.crt
    SSLCertificateKeyFile /etc/ssl/private/your-domain.key
    
    # 目錄配置
    <Directory /var/www/gratitude-diary-web>
        AllowOverride All
        Require all granted
        
        # React Router支援
        FallbackResource /index.html
        
        # 安全標頭
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options SAMEORIGIN
        Header always set X-XSS-Protection "1; mode=block"
        Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
        Header always set Referrer-Policy "strict-origin-when-cross-origin"
    </Directory>
    
    # 靜態資源緩存
    <LocationMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
        Header append Cache-Control "public, immutable"
    </LocationMatch>
    
    # HTML文件不緩存
    <LocationMatch "\.html$">
        ExpiresActive On
        ExpiresDefault "access plus 0 seconds"
        Header set Cache-Control "no-cache, no-store, must-revalidate"
    </LocationMatch>
    
    # 壓縮配置
    <Location />
        SetOutputFilter DEFLATE
        SetEnvIfNoCase Request_URI \
            \.(?:gif|jpe?g|png)$ no-gzip dont-vary
        SetEnvIfNoCase Request_URI \
            \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
    </Location>
    
    # 日誌配置
    ErrorLog ${APACHE_LOG_DIR}/gratitude-diary-web_error.log
    CustomLog ${APACHE_LOG_DIR}/gratitude-diary-web_access.log combined
</VirtualHost>
```

## Firebase FCM設置

### 1. Firebase專案配置
```bash
# 在Firebase Console中：
# 1. 創建新專案或使用現有專案
# 2. 啟用Cloud Messaging
# 3. 生成服務帳戶密鑰
# 4. 配置Web應用並獲取配置信息
# 5. 設置VAPID密鑰
```

### 2. 服務帳戶密鑰配置
```bash
# 下載服務帳戶密鑰文件
# 放置到安全位置
sudo mkdir -p /etc/firebase
sudo mv firebase-service-account.json /etc/firebase/
sudo chmod 600 /etc/firebase/firebase-service-account.json
sudo chown www-data:www-data /etc/firebase/firebase-service-account.json
```

## 監控和日誌

### 1. 日誌配置
```bash
# 創建日誌目錄
sudo mkdir -p /var/log/gratitude-diary
sudo chown www-data:www-data /var/log/gratitude-diary

# 配置logrotate
sudo nano /etc/logrotate.d/gratitude-diary
```

```bash
# /etc/logrotate.d/gratitude-diary
/var/log/gratitude-diary/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload apache2
    endscript
}
```

### 2. 性能監控腳本
```bash
#!/bin/bash
# scripts/monitor.sh

# 檢查服務狀態
check_service() {
    if systemctl is-active --quiet $1; then
        echo "✓ $1 is running"
    else
        echo "✗ $1 is not running"
        systemctl restart $1
    fi
}

# 檢查磁盤空間
check_disk_space() {
    USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ $USAGE -gt 80 ]; then
        echo "⚠ Disk usage is ${USAGE}%"
    else
        echo "✓ Disk usage is ${USAGE}%"
    fi
}

# 檢查內存使用
check_memory() {
    USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ $USAGE -gt 80 ]; then
        echo "⚠ Memory usage is ${USAGE}%"
    else
        echo "✓ Memory usage is ${USAGE}%"
    fi
}

echo "=== System Health Check ==="
check_service apache2
check_service mysql
check_service php8.2-fpm
check_disk_space
check_memory

# 檢查API健康狀態
API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://api.your-domain.com/health)
if [ $API_STATUS -eq 200 ]; then
    echo "✓ API is healthy"
else
    echo "✗ API returned status $API_STATUS"
fi
```

### 3. 設置監控定時任務
```bash
# 每5分鐘檢查一次
*/5 * * * * /var/www/gratitude-diary-api/scripts/monitor.sh >> /var/log/gratitude-diary/monitor.log 2>&1
```

## 安全配置

### 1. 防火牆設置
```bash
# 安裝UFW
sudo apt install -y ufw

# 基本規則
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允許SSH
sudo ufw allow ssh

# 允許HTTP和HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 啟用防火牆
sudo ufw enable
```

### 2. SSL證書配置
```bash
# 使用Let's Encrypt
sudo apt install -y certbot python3-certbot-apache

# 獲取證書
sudo certbot --apache -d your-domain.com -d www.your-domain.com -d api.your-domain.com

# 設置自動續期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. 安全加固
```bash
# 隱藏Apache版本信息
echo "ServerTokens Prod" >> /etc/apache2/apache2.conf
echo "ServerSignature Off" >> /etc/apache2/apache2.conf

# 禁用不必要的模組
sudo a2dismod status
sudo a2dismod info

# 重啟Apache
sudo systemctl restart apache2
```

這個混合架構部署指南提供了完整的生產環境部署方案，確保應用的穩定性、安全性和可擴展性。
