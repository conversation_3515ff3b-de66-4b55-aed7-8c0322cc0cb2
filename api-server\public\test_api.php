<?php
// 測試 API 組件
require_once '../vendor/autoload.php';

use Dotenv\Dotenv;

// 載入環境變數
$dotenv = Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

echo "Testing API components...\n\n";

// 測試數據庫連接
try {
    require_once '../app/Services/Database.php';
    echo "✅ Database service loaded\n";
    
    $db = Database::getInstance();
    echo "✅ Database instance created\n";
    
    $result = $db->fetchOne("SELECT COUNT(*) as count FROM users");
    echo "✅ Database query successful - User count: " . $result['count'] . "\n";
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

// 測試認證服務
try {
    require_once '../app/Services/AuthService.php';
    echo "✅ Auth service loaded\n";
    
    AuthService::init();
    echo "✅ Auth service initialized\n";
    
} catch (Exception $e) {
    echo "❌ Auth service error: " . $e->getMessage() . "\n";
}

// 測試模型
try {
    require_once '../app/Models/BaseModel.php';
    require_once '../app/Models/User.php';
    echo "✅ Models loaded\n";
    
    $userModel = new User();
    echo "✅ User model instantiated\n";
    
} catch (Exception $e) {
    echo "❌ Model error: " . $e->getMessage() . "\n";
}

// 測試控制器
try {
    require_once '../app/Controllers/AuthController.php';
    echo "✅ Controllers loaded\n";
    
} catch (Exception $e) {
    echo "❌ Controller error: " . $e->getMessage() . "\n";
}

// 測試中間件
try {
    require_once '../app/Middleware/CorsMiddleware.php';
    require_once '../app/Middleware/AuthMiddleware.php';
    echo "✅ Middleware loaded\n";
    
} catch (Exception $e) {
    echo "❌ Middleware error: " . $e->getMessage() . "\n";
}

echo "\n✅ All components tested successfully!\n";
