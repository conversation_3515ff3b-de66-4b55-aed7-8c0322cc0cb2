/**
 * 測試導航頁面 - 用於測試首頁連結修復
 */

import React from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Container
} from '@mui/material';
import { useNavigate } from 'react-router-dom';

const TestNavigationPage = () => {
  const navigate = useNavigate();

  const testRoutes = [
    { path: '/', label: '首頁 (公開日記)', description: '應該顯示公開日記列表' },
    { path: '/app', label: '/app (需要認證)', description: '應該跳轉到登入頁面或個人中心' },
    { path: '/app/create', label: '創建日記', description: '需要認證的創建頁面' },
    { path: '/app/focus', label: '專注模式', description: '需要認證的專注頁面' },
    { path: '/app/profile', label: '個人資料', description: '需要認證的個人資料頁面' },
    { path: '/login', label: '登入頁面', description: '用戶登入' },
    { path: '/register', label: '註冊頁面', description: '用戶註冊' },
  ];

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          導航測試頁面
        </Typography>
        
        <Typography variant="body1" gutterBottom sx={{ mb: 3 }}>
          這個頁面用於測試首頁連結修復是否正確。點擊下面的按鈕測試不同的路由：
        </Typography>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {testRoutes.map((route) => (
            <Paper 
              key={route.path} 
              sx={{ 
                p: 2, 
                border: '1px solid #e0e0e0',
                '&:hover': { bgcolor: '#f5f5f5' }
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    {route.label}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {route.description}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    路徑: {route.path}
                  </Typography>
                </Box>
                <Button 
                  variant="contained" 
                  onClick={() => navigate(route.path)}
                  sx={{ minWidth: '100px' }}
                >
                  前往
                </Button>
              </Box>
            </Paper>
          ))}
        </Box>

        <Box sx={{ mt: 4, p: 2, bgcolor: '#f0f8ff', borderRadius: 2 }}>
          <Typography variant="h6" gutterBottom>
            測試說明：
          </Typography>
          <Typography variant="body2" component="div">
            <ul>
              <li><strong>首頁 (/)</strong>：應該顯示公開日記，無需認證</li>
              <li><strong>/app 路由</strong>：需要認證，未登入時跳轉到登入頁面</li>
              <li><strong>底部導航的首頁按鈕</strong>：現在應該導航到 "/" 而不是 "/app"</li>
              <li><strong>修復前問題</strong>：首頁按鈕導航到 /app，但 /app 的 index 指向個人中心</li>
              <li><strong>修復後效果</strong>：首頁按鈕導航到 /，顯示公開日記</li>
            </ul>
          </Typography>
        </Box>
      </Paper>
    </Container>
  );
};

export default TestNavigationPage;
