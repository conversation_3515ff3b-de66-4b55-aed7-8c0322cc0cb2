<?php
require_once 'vendor/autoload.php';

use Dotenv\Dotenv;

// 載入環境變數
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();

// 測試數據庫連接
try {
    $config = require 'config/database.php';
    
    $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
    
    echo "嘗試連接數據庫...\n";
    echo "Host: {$config['host']}\n";
    echo "Database: {$config['database']}\n";
    echo "Username: {$config['username']}\n";
    echo "password: {$config['password']}\n";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    
    echo "✅ 數據庫連接成功！\n\n";
    
    // 檢查表是否存在
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "現有表格:\n";
    foreach ($tables as $table) {
        echo "- $table\n";
    }
    
    if (empty($tables)) {
        echo "❌ 沒有找到任何表格，需要運行數據庫遷移\n";
    }
    
} catch (PDOException $e) {
    echo "❌ 數據庫連接失敗: " . $e->getMessage() . "\n";
    echo "請檢查:\n";
    echo "1. MySQL 服務是否運行\n";
    echo "2. 數據庫配置是否正確\n";
    echo "3. 用戶權限是否正確\n";
} catch (Exception $e) {
    echo "❌ 錯誤: " . $e->getMessage() . "\n";
}
