import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  AppBar,
  Toolbar,
  IconButton,
  Card,
  CardContent,
  TextField,
  Button,
  Chip,
  Rating,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Snackbar,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import BugReportIcon from '@mui/icons-material/BugReport';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';
import QuestionAnswerIcon from '@mui/icons-material/QuestionAnswer';
import SendIcon from '@mui/icons-material/Send';
import StarIcon from '@mui/icons-material/Star';
import { motion } from 'framer-motion';

const FeedbackPage = () => {
  const navigate = useNavigate();
  const [feedbackForm, setFeedbackForm] = useState({
    type: 'suggestion',
    rating: 5,
    subject: '',
    description: '',
    email: '',
    category: '',
  });
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  const handleBack = () => {
    navigate('/app/profile');
  };

  const handleSubmit = () => {
    console.log('提交反饋:', feedbackForm);
    setShowSuccessMessage(true);
    // 重置表單
    setFeedbackForm({
      type: 'suggestion',
      rating: 5,
      subject: '',
      description: '',
      email: '',
      category: '',
    });
  };

  const feedbackTypes = [
    {
      id: 'bug',
      label: '錯誤回報',
      icon: <BugReportIcon />,
      color: 'error.main',
      description: '回報應用中的錯誤或問題',
    },
    {
      id: 'suggestion',
      label: '功能建議',
      icon: <LightbulbIcon />,
      color: 'warning.main',
      description: '建議新功能或改進現有功能',
    },
    {
      id: 'praise',
      label: '表揚讚美',
      icon: <ThumbUpIcon />,
      color: 'success.main',
      description: '分享您喜歡的功能或體驗',
    },
    {
      id: 'question',
      label: '使用問題',
      icon: <QuestionAnswerIcon />,
      color: 'info.main',
      description: '詢問使用方法或功能說明',
    },
  ];

  const categories = {
    bug: ['界面問題', '功能異常', '性能問題', '數據錯誤', '其他'],
    suggestion: ['新功能', '界面改進', '用戶體驗', '性能優化', '其他'],
    praise: ['界面設計', '功能實用', '用戶體驗', '整體滿意', '其他'],
    question: ['使用方法', '功能說明', '帳戶問題', '技術支援', '其他'],
  };

  const getCurrentCategories = () => {
    return categories[feedbackForm.type] || [];
  };

  const isFormValid = () => {
    return feedbackForm.subject.trim() && 
           feedbackForm.description.trim() && 
           feedbackForm.category;
  };

  return (
    <Box sx={{ 
      bgcolor: 'background.default', 
      minHeight: '100vh',
      width: '100%',
      maxWidth: '100vw',
      margin: '0 auto',
    }}>
      {/* 頂部導航欄 */}
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar sx={{ maxWidth: '600px', width: '100%', margin: '0 auto' }}>
          <IconButton onClick={handleBack} sx={{ mr: 2 }}>
            <ArrowBackIcon sx={{ color: 'text.primary' }} />
          </IconButton>
          <Typography
            variant="h3"
            sx={{
              flex: 1,
              color: 'text.primary',
              fontWeight: 600,
            }}
          >
            意見反饋
          </Typography>
        </Toolbar>
      </AppBar>

      <Box sx={{ 
        p: 2,
        maxWidth: '600px',
        width: '100%',
        margin: '0 auto',
      }}>
        {/* 反饋類型選擇 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            選擇反饋類型
          </Typography>
          <Card sx={{ mb: 3, borderRadius: 4 }}>
            <List sx={{ py: 0 }}>
              {feedbackTypes.map((type, index) => (
                <React.Fragment key={type.id}>
                  <ListItem
                    button
                    onClick={() => setFeedbackForm(prev => ({ 
                      ...prev, 
                      type: type.id,
                      category: '', // 重置分類
                    }))}
                    sx={{
                      py: 2,
                      bgcolor: feedbackForm.type === type.id ? `${type.color}10` : 'transparent',
                      borderLeft: feedbackForm.type === type.id ? `4px solid ${type.color}` : 'none',
                    }}
                  >
                    <ListItemIcon sx={{ color: type.color }}>
                      {type.icon}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {type.label}
                        </Typography>
                      }
                      secondary={
                        <Typography variant="body2" color="text.secondary">
                          {type.description}
                        </Typography>
                      }
                    />
                    <Radio
                      checked={feedbackForm.type === type.id}
                      sx={{ color: type.color }}
                    />
                  </ListItem>
                  {index < feedbackTypes.length - 1 && <Divider variant="inset" component="li" />}
                </React.Fragment>
              ))}
            </List>
          </Card>
        </motion.div>

        {/* 評分（僅在表揚時顯示） */}
        {feedbackForm.type === 'praise' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <Card sx={{ mb: 3, borderRadius: 4 }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                  整體評分
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Rating
                    value={feedbackForm.rating}
                    onChange={(event, newValue) => {
                      setFeedbackForm(prev => ({ ...prev, rating: newValue }));
                    }}
                    size="large"
                    icon={<StarIcon fontSize="inherit" />}
                  />
                  <Typography variant="body1" color="text.secondary">
                    {feedbackForm.rating} 星
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* 詳細分類 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            詳細分類
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 3 }}>
            {getCurrentCategories().map((category) => (
              <Chip
                key={category}
                label={category}
                onClick={() => setFeedbackForm(prev => ({ ...prev, category }))}
                variant={feedbackForm.category === category ? 'filled' : 'outlined'}
                color={feedbackForm.category === category ? 'primary' : 'default'}
                sx={{ cursor: 'pointer' }}
              />
            ))}
          </Box>
        </motion.div>

        {/* 反饋內容 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Card sx={{ mb: 3, borderRadius: 4 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                詳細描述
              </Typography>
              
              <TextField
                fullWidth
                label="標題"
                value={feedbackForm.subject}
                onChange={(e) => setFeedbackForm(prev => ({ ...prev, subject: e.target.value }))}
                sx={{ mb: 3 }}
                placeholder="簡要描述您的反饋"
              />

              <TextField
                fullWidth
                label="詳細描述"
                value={feedbackForm.description}
                onChange={(e) => setFeedbackForm(prev => ({ ...prev, description: e.target.value }))}
                multiline
                rows={6}
                sx={{ mb: 3 }}
                placeholder={
                  feedbackForm.type === 'bug' 
                    ? "請詳細描述遇到的問題，包括操作步驟、錯誤信息等..."
                    : feedbackForm.type === 'suggestion'
                    ? "請描述您希望添加或改進的功能，以及預期的效果..."
                    : feedbackForm.type === 'praise'
                    ? "請分享您喜歡的功能或體驗，讓我們知道做得好的地方..."
                    : "請描述您遇到的問題或需要幫助的地方..."
                }
              />

              <TextField
                fullWidth
                label="聯繫郵箱（選填）"
                value={feedbackForm.email}
                onChange={(e) => setFeedbackForm(prev => ({ ...prev, email: e.target.value }))}
                type="email"
                placeholder="如需回覆，請留下您的郵箱"
                helperText="我們會在必要時與您聯繫"
              />
            </CardContent>
          </Card>
        </motion.div>

        {/* 提交按鈕 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <Button
            fullWidth
            variant="contained"
            size="large"
            onClick={handleSubmit}
            disabled={!isFormValid()}
            startIcon={<SendIcon />}
            sx={{
              py: 1.5,
              borderRadius: 3,
              fontWeight: 600,
              fontSize: '1.1rem',
            }}
          >
            提交反饋
          </Button>
        </motion.div>

        {/* 溫馨提示 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
        >
          <Card sx={{ mt: 3, borderRadius: 4, bgcolor: 'info.light', color: 'info.main' }}>
            <CardContent>
              <Typography variant="body2" sx={{ textAlign: 'center' }}>
                💡 您的反饋對我們非常重要！我們會認真閱讀每一條反饋，並持續改進產品體驗。
              </Typography>
            </CardContent>
          </Card>
        </motion.div>
      </Box>

      {/* 成功提示 */}
      <Snackbar
        open={showSuccessMessage}
        autoHideDuration={4000}
        onClose={() => setShowSuccessMessage(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert 
          onClose={() => setShowSuccessMessage(false)} 
          severity="success"
          sx={{ width: '100%' }}
        >
          反饋提交成功！感謝您的寶貴意見 🙏
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default FeedbackPage;
