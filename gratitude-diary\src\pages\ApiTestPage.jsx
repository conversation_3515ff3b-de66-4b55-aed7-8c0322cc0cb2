/**
 * API 測試頁面
 */

import React, { useState } from 'react';
import {
  Container,
  Paper,
  Button,
  Typography,
  Box,
  TextField,
  Alert
} from '@mui/material';
import { apiClient } from '../services/api.js';

const ApiTestPage = () => {
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password123');
  const [displayName, setDisplayName] = useState('測試用戶');

  const testHealthCheck = async () => {
    setLoading(true);
    try {
      console.log('Testing health check...');
      console.log('API Base URL:', apiClient.baseURL);
      
      const response = await apiClient.get('/api/v1/health');
      console.log('Health check response:', response);
      setResult(JSON.stringify(response, null, 2));
    } catch (error) {
      console.error('Health check error:', error);
      setResult(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testRegister = async () => {
    setLoading(true);
    try {
      console.log('Testing register...');
      console.log('API Base URL:', apiClient.baseURL);
      
      const userData = {
        email,
        password,
        display_name: displayName
      };
      
      console.log('Register data:', userData);
      
      const response = await apiClient.post('/api/v1/auth/register', userData);
      console.log('Register response:', response);
      setResult(JSON.stringify(response, null, 2));
    } catch (error) {
      console.error('Register error:', error);
      setResult(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          API 測試頁面
        </Typography>
        
        <Typography variant="body1" gutterBottom>
          API Base URL: {import.meta.env.VITE_API_BASE_URL}
        </Typography>

        <Box sx={{ my: 3 }}>
          <Button 
            variant="contained" 
            onClick={testHealthCheck}
            disabled={loading}
            sx={{ mr: 2 }}
          >
            測試健康檢查
          </Button>
          
          <Button 
            variant="contained" 
            onClick={testRegister}
            disabled={loading}
            color="secondary"
          >
            測試註冊
          </Button>
        </Box>

        <Box sx={{ my: 3 }}>
          <TextField
            label="Email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            fullWidth
            margin="normal"
          />
          <TextField
            label="Password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            fullWidth
            margin="normal"
          />
          <TextField
            label="Display Name"
            value={displayName}
            onChange={(e) => setDisplayName(e.target.value)}
            fullWidth
            margin="normal"
          />
        </Box>

        {result && (
          <Box sx={{ mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              結果：
            </Typography>
            <Alert severity={result.includes('Error') ? 'error' : 'success'}>
              <pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
                {result}
              </pre>
            </Alert>
          </Box>
        )}
      </Paper>
    </Container>
  );
};

export default ApiTestPage;
