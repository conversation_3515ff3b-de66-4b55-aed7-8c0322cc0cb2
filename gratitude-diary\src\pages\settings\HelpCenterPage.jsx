import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  AppBar,
  Toolbar,
  IconButton,
  Card,
  CardContent,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import QuestionAnswerIcon from '@mui/icons-material/QuestionAnswer';
import VideoLibraryIcon from '@mui/icons-material/VideoLibrary';
import ArticleIcon from '@mui/icons-material/Article';
import ContactSupportIcon from '@mui/icons-material/ContactSupport';
import { motion } from 'framer-motion';

const HelpCenterPage = () => {
  const navigate = useNavigate();
  const [showContactDialog, setShowContactDialog] = useState(false);
  const [contactForm, setContactForm] = useState({
    subject: '',
    message: '',
    category: 'general',
  });

  const handleBack = () => {
    navigate('/app/settings');
  };

  const faqData = [
    {
      category: '基本使用',
      questions: [
        {
          question: '如何開始記錄我的第一篇感恩日記？',
          answer: '點擊底部導航的「記錄」按鈕，然後在輸入框中寫下今天讓你感恩的事情。你可以選擇心情、添加標籤，甚至使用我們的引導問題來獲得靈感。',
        },
        {
          question: '什麼是情感流動卡片牆？',
          answer: '這是我們的首頁特色功能，以瀑布流的方式展示所有用戶的感恩分享。每個卡片都有對應的情感色彩，讓你可以視覺化地感受不同的情緒狀態。',
        },
        {
          question: '如何使用專注工作功能？',
          answer: '進入「專注」頁面，設定你的工作時間（預設25分鐘）和休息時間（預設10分鐘）。點擊開始後，系統會在工作結束時顯示感謝詞，提醒你休息。',
        },
      ],
    },
    {
      category: '隱私與安全',
      questions: [
        {
          question: '我的日記內容會被其他人看到嗎？',
          answer: '你可以選擇公開分享、匿名分享或僅限好友可見。私人日記只有你自己能看到，我們非常重視用戶隱私。',
        },
        {
          question: '如何刪除我的帳戶和數據？',
          answer: '在設定頁面中選擇「隱私設定」，然後選擇「刪除帳戶」。請注意，這個操作是不可逆的。',
        },
      ],
    },
    {
      category: '功能問題',
      questions: [
        {
          question: '為什麼我收不到每日提醒通知？',
          answer: '請檢查：1) 瀏覽器是否允許通知權限 2) 在通知設定中是否開啟了每日提醒 3) 設定的提醒時間是否正確。',
        },
        {
          question: '如何自定義專注工作的感謝詞？',
          answer: '在專注頁面點擊右上角的設定按鈕，然後編輯感謝詞模板。你可以使用 {$workTime} 和 {$breakTime} 作為時間變數。',
        },
      ],
    },
  ];

  const helpResources = [
    {
      icon: <VideoLibraryIcon />,
      title: '視頻教學',
      description: '觀看詳細的功能介紹視頻',
      action: () => console.log('視頻教學'),
    },
    {
      icon: <ArticleIcon />,
      title: '使用指南',
      description: '閱讀完整的使用說明文檔',
      action: () => console.log('使用指南'),
    },
    {
      icon: <ContactSupportIcon />,
      title: '聯繫客服',
      description: '直接與我們的支援團隊聯繫',
      action: () => setShowContactDialog(true),
    },
  ];

  const handleContactSubmit = () => {
    console.log('提交聯繫表單:', contactForm);
    setShowContactDialog(false);
    setContactForm({ subject: '', message: '', category: 'general' });
  };

  return (
    <Box sx={{ 
      bgcolor: 'background.default', 
      minHeight: '100vh',
      width: '100%',
      maxWidth: '100vw',
      margin: '0 auto',
    }}>
      {/* 頂部導航欄 */}
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar sx={{ maxWidth: '600px', width: '100%', margin: '0 auto' }}>
          <IconButton onClick={handleBack} sx={{ mr: 2 }}>
            <ArrowBackIcon sx={{ color: 'text.primary' }} />
          </IconButton>
          <Typography
            variant="h3"
            sx={{
              flex: 1,
              color: 'text.primary',
              fontWeight: 600,
            }}
          >
            幫助中心
          </Typography>
        </Toolbar>
      </AppBar>

      <Box sx={{ 
        p: 2,
        maxWidth: '600px',
        width: '100%',
        margin: '0 auto',
      }}>
        {/* 快速幫助 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            快速幫助
          </Typography>
          <Box sx={{ display: 'grid', gap: 2, mb: 3 }}>
            {helpResources.map((resource, index) => (
              <Card
                key={index}
                sx={{
                  borderRadius: 3,
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                  },
                }}
                onClick={resource.action}
              >
                <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box sx={{ color: 'primary.main' }}>
                    {resource.icon}
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {resource.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {resource.description}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            ))}
          </Box>
        </motion.div>

        {/* 常見問題 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            常見問題
          </Typography>
          {faqData.map((category, categoryIndex) => (
            <Box key={categoryIndex} sx={{ mb: 3 }}>
              <Typography 
                variant="subtitle1" 
                sx={{ 
                  mb: 1, 
                  fontWeight: 600, 
                  color: 'primary.main',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                }}
              >
                <QuestionAnswerIcon fontSize="small" />
                {category.category}
              </Typography>
              {category.questions.map((faq, faqIndex) => (
                <Accordion
                  key={faqIndex}
                  sx={{
                    mb: 1,
                    borderRadius: 2,
                    '&:before': { display: 'none' },
                    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                  }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    sx={{
                      '& .MuiAccordionSummary-content': {
                        margin: '12px 0',
                      },
                    }}
                  >
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {faq.question}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                      {faq.answer}
                    </Typography>
                  </AccordionDetails>
                </Accordion>
              ))}
            </Box>
          ))}
        </motion.div>

        {/* 聯繫我們 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card sx={{ borderRadius: 4, bgcolor: 'primary.light', color: 'primary.main' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
                還有其他問題？
              </Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>
                我們的支援團隊隨時為您提供幫助
              </Typography>
              <Button
                variant="contained"
                onClick={() => setShowContactDialog(true)}
                sx={{
                  bgcolor: 'primary.main',
                  color: 'white',
                  '&:hover': {
                    bgcolor: 'primary.dark',
                  },
                }}
              >
                聯繫客服
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </Box>

      {/* 聯繫客服對話框 */}
      <Dialog
        open={showContactDialog}
        onClose={() => setShowContactDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>聯繫客服</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
            {['general', 'bug', 'feature', 'account'].map((cat) => (
              <Chip
                key={cat}
                label={{
                  general: '一般問題',
                  bug: '錯誤回報',
                  feature: '功能建議',
                  account: '帳戶問題',
                }[cat]}
                onClick={() => setContactForm(prev => ({ ...prev, category: cat }))}
                variant={contactForm.category === cat ? 'filled' : 'outlined'}
                color={contactForm.category === cat ? 'primary' : 'default'}
              />
            ))}
          </Box>
          <TextField
            fullWidth
            label="主題"
            value={contactForm.subject}
            onChange={(e) => setContactForm(prev => ({ ...prev, subject: e.target.value }))}
            sx={{ mb: 2 }}
          />
          <TextField
            fullWidth
            label="詳細描述"
            value={contactForm.message}
            onChange={(e) => setContactForm(prev => ({ ...prev, message: e.target.value }))}
            multiline
            rows={4}
            placeholder="請詳細描述您遇到的問題或建議..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowContactDialog(false)}>
            取消
          </Button>
          <Button 
            onClick={handleContactSubmit} 
            variant="contained"
            disabled={!contactForm.subject || !contactForm.message}
          >
            發送
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default HelpCenterPage;
