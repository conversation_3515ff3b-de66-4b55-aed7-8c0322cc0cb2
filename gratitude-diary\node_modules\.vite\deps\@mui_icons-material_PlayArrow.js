"use client";
import "./chunk-C6WWHQR7.js";
import {
  createSvgIcon
} from "./chunk-QVI57TQV.js";
import "./chunk-YZ6PAUYO.js";
import "./chunk-BRYCWNNY.js";
import "./chunk-6YD57FQ6.js";
import {
  require_jsx_runtime
} from "./chunk-3NBMPMSA.js";
import {
  __toESM
} from "./chunk-DAFPDBRK.js";

// node_modules/@mui/icons-material/esm/PlayArrow.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var PlayArrow_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "M8 5v14l11-7z"
}), "PlayArrow");
export {
  PlayArrow_default as default
};
//# sourceMappingURL=@mui_icons-material_PlayArrow.js.map
