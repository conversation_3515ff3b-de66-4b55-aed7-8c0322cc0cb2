<?php
require_once __DIR__ . '/../../vendor/autoload.php';
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

/**
 * 認證服務
 * 處理JWT token生成、驗證、密碼加密等認證相關功能
 */
class AuthService {
    private static $secretKey;
    private static $algorithm = 'HS256';
    private static $expireTime = 86400; // 24小時
    private static $issuer;
    private static $audience;

    public static function init() {
        self::$secretKey = $_ENV['JWT_SECRET'] ?? 'default-secret-key-change-in-production';
        self::$expireTime = $_ENV['JWT_EXPIRE'] ?? 86400;
        self::$issuer = $_ENV['APP_URL'] ?? 'http://localhost';
        self::$audience = $_ENV['APP_URL'] ?? 'http://localhost';
        
        // 確保密鑰長度足夠
        if (strlen(self::$secretKey) < 32) {
            throw new Exception('JWT secret key must be at least 32 characters long');
        }
    }

    /**
     * 生成JWT token
     */
    public static function generateToken($userId, $email, $additionalClaims = []) {
        $now = time();
        $payload = array_merge([
            'iss' => self::$issuer,           // 發行者
            'aud' => self::$audience,         // 接收者
            'iat' => $now,                    // 發行時間
            'exp' => $now + self::$expireTime, // 過期時間
            'nbf' => $now,                    // 生效時間
            'jti' => uniqid(),                // JWT ID
            'user_id' => $userId,
            'email' => $email
        ], $additionalClaims);

        return JWT::encode($payload, self::$secretKey, self::$algorithm);
    }

    /**
     * 驗證JWT token
     */
    public static function validateToken($token) {
        try {
            $decoded = JWT::decode($token, new Key(self::$secretKey, self::$algorithm));
            return (array) $decoded;
        } catch (Exception $e) {
            error_log("Token validation failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 從請求頭獲取並驗證token
     */
    public static function getCurrentUser() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        
        if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return false;
        }

        $token = $matches[1];
        $payload = self::validateToken($token);
        
        if (!$payload) {
            return false;
        }

        // 檢查token是否過期
        if (isset($payload['exp']) && $payload['exp'] < time()) {
            return false;
        }

        return $payload;
    }

    /**
     * 生成刷新token
     */
    public static function generateRefreshToken($userId) {
        $payload = [
            'iss' => self::$issuer,
            'aud' => self::$audience,
            'iat' => time(),
            'exp' => time() + (7 * 24 * 60 * 60), // 7天
            'user_id' => $userId,
            'type' => 'refresh'
        ];

        return JWT::encode($payload, self::$secretKey, self::$algorithm);
    }

    /**
     * 驗證刷新token
     */
    public static function validateRefreshToken($token) {
        $payload = self::validateToken($token);
        
        if (!$payload || !isset($payload['type']) || $payload['type'] !== 'refresh') {
            return false;
        }

        return $payload;
    }

    /**
     * 密碼加密
     */
    public static function hashPassword($password) {
        // 驗證密碼強度
        if (!self::validatePasswordStrength($password)) {
            throw new Exception('Password does not meet security requirements');
        }

        return password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
    }

    /**
     * 驗證密碼
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }

    /**
     * 驗證密碼強度
     */
    public static function validatePasswordStrength($password) {
        // 至少8個字符
        if (strlen($password) < 8) {
            return false;
        }

        // 包含至少一個數字和一個字母
        if (!preg_match('/^(?=.*[A-Za-z])(?=.*\d)/', $password)) {
            return false;
        }

        return true;
    }

    /**
     * 生成隨機密碼
     */
    public static function generateRandomPassword($length = 12) {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        $password = '';
        
        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[random_int(0, strlen($characters) - 1)];
        }
        
        return $password;
    }

    /**
     * 生成重置密碼token
     */
    public static function generatePasswordResetToken($userId, $email) {
        $payload = [
            'iss' => self::$issuer,
            'aud' => self::$audience,
            'iat' => time(),
            'exp' => time() + (60 * 60), // 1小時
            'user_id' => $userId,
            'email' => $email,
            'type' => 'password_reset'
        ];

        return JWT::encode($payload, self::$secretKey, self::$algorithm);
    }

    /**
     * 驗證重置密碼token
     */
    public static function validatePasswordResetToken($token) {
        $payload = self::validateToken($token);
        
        if (!$payload || !isset($payload['type']) || $payload['type'] !== 'password_reset') {
            return false;
        }

        return $payload;
    }

    /**
     * 生成郵箱驗證token
     */
    public static function generateEmailVerificationToken($userId, $email) {
        $payload = [
            'iss' => self::$issuer,
            'aud' => self::$audience,
            'iat' => time(),
            'exp' => time() + (24 * 60 * 60), // 24小時
            'user_id' => $userId,
            'email' => $email,
            'type' => 'email_verification'
        ];

        return JWT::encode($payload, self::$secretKey, self::$algorithm);
    }

    /**
     * 驗證郵箱驗證token
     */
    public static function validateEmailVerificationToken($token) {
        $payload = self::validateToken($token);
        
        if (!$payload || !isset($payload['type']) || $payload['type'] !== 'email_verification') {
            return false;
        }

        return $payload;
    }

    /**
     * 檢查用戶權限
     */
    public static function checkPermission($user, $permission) {
        // 基本權限檢查邏輯
        if (!isset($user['user_id'])) {
            return false;
        }

        // 這裡可以擴展更複雜的權限系統
        switch ($permission) {
            case 'create_entry':
            case 'update_own_entry':
            case 'delete_own_entry':
            case 'like_entry':
            case 'comment_entry':
                return true;
            
            case 'admin':
                return isset($user['role']) && $user['role'] === 'admin';
            
            default:
                return false;
        }
    }

    /**
     * 生成API密鑰
     */
    public static function generateApiKey($userId) {
        $data = $userId . time() . random_bytes(16);
        return hash('sha256', $data);
    }

    /**
     * 驗證API密鑰
     */
    public static function validateApiKey($apiKey) {
        // 這裡應該從數據庫查詢API密鑰
        // 暫時返回false，需要實現API密鑰存儲邏輯
        return false;
    }

    /**
     * 記錄登入嘗試
     */
    public static function logLoginAttempt($email, $success, $ip = null) {
        $ip = $ip ?: ($_SERVER['REMOTE_ADDR'] ?? 'unknown');
        
        // 記錄到日誌文件
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'email' => $email,
            'success' => $success ? 'SUCCESS' : 'FAILED',
            'ip' => $ip,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];
        
        $logLine = json_encode($logData) . "\n";
        file_put_contents(__DIR__ . '/../../storage/logs/auth.log', $logLine, FILE_APPEND | LOCK_EX);
    }

    /**
     * 檢查登入嘗試頻率限制
     */
    public static function checkRateLimit($email, $maxAttempts = 5, $timeWindow = 900) { // 15分鐘
        $logFile = __DIR__ . '/../../storage/logs/auth.log';
        
        if (!file_exists($logFile)) {
            return true;
        }

        $lines = file($logFile, FILE_IGNORE_NEW_LINES);
        $recentAttempts = 0;
        $cutoffTime = time() - $timeWindow;

        foreach (array_reverse($lines) as $line) {
            $data = json_decode($line, true);
            if (!$data) continue;

            $attemptTime = strtotime($data['timestamp']);
            if ($attemptTime < $cutoffTime) {
                break;
            }

            if ($data['email'] === $email && $data['success'] === 'FAILED') {
                $recentAttempts++;
            }
        }

        return $recentAttempts < $maxAttempts;
    }

    /**
     * 清理過期的日誌
     */
    public static function cleanupLogs($days = 30) {
        $logFile = __DIR__ . '/../../storage/logs/auth.log';
        
        if (!file_exists($logFile)) {
            return;
        }

        $lines = file($logFile, FILE_IGNORE_NEW_LINES);
        $cutoffTime = time() - ($days * 24 * 60 * 60);
        $validLines = [];

        foreach ($lines as $line) {
            $data = json_decode($line, true);
            if (!$data) continue;

            $attemptTime = strtotime($data['timestamp']);
            if ($attemptTime >= $cutoffTime) {
                $validLines[] = $line;
            }
        }

        file_put_contents($logFile, implode("\n", $validLines) . "\n");
    }

    /**
     * 獲取token剩餘有效時間
     */
    public static function getTokenRemainingTime($token) {
        $payload = self::validateToken($token);
        
        if (!$payload || !isset($payload['exp'])) {
            return 0;
        }

        return max(0, $payload['exp'] - time());
    }

    /**
     * 檢查token是否即將過期
     */
    public static function isTokenExpiringSoon($token, $threshold = 3600) { // 1小時
        $remainingTime = self::getTokenRemainingTime($token);
        return $remainingTime > 0 && $remainingTime < $threshold;
    }
}

// 初始化認證服務
AuthService::init();
