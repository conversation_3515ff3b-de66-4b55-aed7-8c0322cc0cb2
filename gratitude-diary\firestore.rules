rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // 輔助函數
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isValidUser() {
      return isAuthenticated() && 
             request.auth.uid != null && 
             request.auth.uid.size() > 0;
    }
    
    // 用戶文檔規則
    match /users/{userId} {
      allow read: if isAuthenticated() && (
        isOwner(userId) || 
        resource.data.preferences.privacy.profileVisible == true
      );
      
      allow create: if isValidUser() && 
                   isOwner(userId) && 
                   validateUserData();
      
      allow update: if isValidUser() && 
                   isOwner(userId) && 
                   validateUserUpdate();
      
      allow delete: if isValidUser() && isOwner(userId);
      
      function validateUserData() {
        let required = ['email', 'displayName', 'preferences', 'stats'];
        return request.resource.data.keys().hasAll(required) &&
               request.resource.data.email is string &&
               request.resource.data.displayName is string;
      }
      
      function validateUserUpdate() {
        let allowedFields = ['displayName', 'avatar', 'bio', 'preferences', 'stats', 'updatedAt'];
        return request.resource.data.diff(resource.data).affectedKeys()
               .hasOnly(allowedFields);
      }
    }
    
    // 日記條目規則
    match /entries/{entryId} {
      allow read: if isAuthenticated() && canReadEntry();
      
      allow create: if isValidUser() && 
                   isOwner(request.resource.data.userId) && 
                   validateEntryData();
      
      allow update: if isValidUser() && 
                   isOwner(resource.data.userId) && 
                   validateEntryUpdate();
      
      allow delete: if isValidUser() && isOwner(resource.data.userId);
      
      function canReadEntry() {
        return isOwner(resource.data.userId) ||
               resource.data.privacy == 'public' ||
               (resource.data.privacy == 'friends' && 
                isOwner(resource.data.userId)); // 簡化版，實際需要好友關係檢查
      }
      
      function validateEntryData() {
        let required = ['userId', 'content', 'emotion', 'privacy', 'createdAt'];
        let allowed = ['userId', 'content', 'emotion', 'tags', 'media', 'privacy', 
                      'isAnonymous', 'location', 'interactions', 'createdAt', 'updatedAt'];
        
        return request.resource.data.keys().hasAll(required) &&
               request.resource.data.keys().hasOnly(allowed) &&
               request.resource.data.content is string &&
               request.resource.data.content.size() > 0 &&
               request.resource.data.content.size() <= 5000 &&
               request.resource.data.emotion is string &&
               request.resource.data.privacy in ['public', 'friends', 'private'] &&
               request.resource.data.userId == request.auth.uid;
      }
      
      function validateEntryUpdate() {
        let allowedFields = ['content', 'emotion', 'tags', 'media', 'privacy', 
                           'isAnonymous', 'location', 'updatedAt'];
        return request.resource.data.diff(resource.data).affectedKeys()
               .hasOnly(allowedFields) &&
               request.resource.data.userId == resource.data.userId;
      }
    }
    
    // 互動記錄規則（點讚、評論等）
    match /interactions/{interactionId} {
      allow read: if isAuthenticated();
      
      allow create: if isValidUser() && 
                   isOwner(request.resource.data.userId) && 
                   validateInteractionData();
      
      allow delete: if isValidUser() && isOwner(resource.data.userId);
      
      function validateInteractionData() {
        let required = ['userId', 'entryId', 'type', 'createdAt'];
        let allowed = ['userId', 'entryId', 'type', 'content', 'parentId', 'createdAt'];
        
        return request.resource.data.keys().hasAll(required) &&
               request.resource.data.keys().hasOnly(allowed) &&
               request.resource.data.type in ['like', 'comment', 'share'] &&
               request.resource.data.userId == request.auth.uid;
      }
    }
    
    // 專注工作記錄規則
    match /focusSessions/{sessionId} {
      allow read, write: if isValidUser() && 
                        isOwner(resource.data.userId);
      
      allow create: if isValidUser() && 
                   isOwner(request.resource.data.userId) && 
                   validateFocusSessionData();
      
      function validateFocusSessionData() {
        let required = ['userId', 'workDuration', 'breakDuration', 'startTime'];
        let allowed = ['userId', 'workDuration', 'breakDuration', 'actualWorkTime',
                      'gratitudeMessage', 'isCompleted', 'startTime', 'endTime', 'createdAt'];
        
        return request.resource.data.keys().hasAll(required) &&
               request.resource.data.keys().hasOnly(allowed) &&
               request.resource.data.workDuration is number &&
               request.resource.data.workDuration >= 5 &&
               request.resource.data.workDuration <= 120 &&
               request.resource.data.userId == request.auth.uid;
      }
    }
    
    // 通知規則
    match /notifications/{notificationId} {
      allow read: if isValidUser() && isOwner(resource.data.userId);
      allow update: if isValidUser() && 
                   isOwner(resource.data.userId) && 
                   request.resource.data.diff(resource.data).affectedKeys()
                   .hasOnly(['isRead', 'readAt']);
      allow delete: if isValidUser() && isOwner(resource.data.userId);
    }
    
    // 成就規則
    match /achievements/{achievementId} {
      allow read: if isAuthenticated();
      // 成就只能由系統創建，用戶不能直接修改
    }
    
    // 系統配置（只讀）
    match /config/{configId} {
      allow read: if isAuthenticated();
    }
    
    // 統計數據（只讀）
    match /stats/{statsId} {
      allow read: if isAuthenticated();
    }
  }
}
