{"openapi": "3.0.3", "info": {"title": "感恩日記 API", "description": "感恩日記應用的 RESTful API 文檔\n\n## 認證\nAPI 使用 JWT Bearer Token 進行認證。在需要認證的端點中，請在 Authorization header 中包含 Bearer token。\n\n## 響應格式\n所有響應都遵循統一的格式：\n- 成功響應：`{\"success\": true, \"data\": {...}}`\n- 錯誤響應：`{\"success\": false, \"error\": \"錯誤信息\"}`\n", "version": "1.0.0", "contact": {"name": "感恩日記 API 支持", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "http://localhost/api-server/public", "description": "本地開發服務器"}, {"url": "https://api.gratitude-diary.com", "description": "生產環境服務器"}], "paths": {"/health": {"get": {"summary": "健康檢查", "description": "檢查 API 服務器狀態和數據庫連接", "tags": ["System"], "responses": {"200": {"description": "服務器正常運行", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "API is running"}, "timestamp": {"type": "string", "format": "date-time", "example": "2024-01-15 10:30:00"}, "user_count": {"type": "integer", "example": 150}, "version": {"type": "string", "example": "1.0.0"}}}}}}}}}, "/api/v1/auth/register": {"post": {"summary": "用戶註冊", "description": "創建新用戶帳戶", "tags": ["Authentication"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "password", "display_name"], "properties": {"email": {"type": "string", "format": "email", "description": "用戶電子郵件地址", "example": "<EMAIL>"}, "password": {"type": "string", "minLength": 6, "description": "用戶密碼（至少6個字符）", "example": "password123"}, "display_name": {"type": "string", "description": "用戶顯示名稱", "example": "張三"}, "bio": {"type": "string", "description": "用戶個人簡介", "example": "熱愛生活的感恩者"}, "language": {"type": "string", "description": "用戶語言偏好", "default": "zh-TW", "example": "zh-TW"}, "timezone": {"type": "string", "description": "用戶時區", "default": "Asia/Taipei", "example": "Asia/Taipei"}}}}}}, "responses": {"201": {"description": "註冊成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "User registered successfully"}, "user": {"$ref": "#/components/schemas/User"}, "token": {"type": "string", "description": "JWT 認證令牌", "example": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "409": {"description": "電子郵件已存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Email already exists"}}}}}}}, "/api/v1/auth/login": {"post": {"summary": "用戶登入", "description": "使用電子郵件和密碼登入", "tags": ["Authentication"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email", "description": "用戶電子郵件地址", "example": "<EMAIL>"}, "password": {"type": "string", "description": "用戶密碼", "example": "password123"}}}}}}, "responses": {"200": {"description": "登入成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Login successful"}, "user": {"$ref": "#/components/schemas/User"}, "token": {"type": "string", "description": "JWT 認證令牌", "example": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"description": "認證失敗", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Invalid credentials"}}}}}}}, "/api/v1/auth/me": {"get": {"summary": "獲取當前用戶信息", "description": "獲取已認證用戶的詳細信息、統計數據和成就", "tags": ["Authentication"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "成功獲取用戶信息", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "user": {"$ref": "#/components/schemas/User"}, "stats": {"$ref": "#/components/schemas/UserStats"}, "achievements": {"type": "array", "items": {"$ref": "#/components/schemas/Achievement"}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/v1/auth/logout": {"post": {"summary": "用戶登出", "description": "登出當前用戶（客戶端處理token失效）", "tags": ["Authentication"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "登出成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Logout successful"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/v1/entries/public": {"get": {"summary": "獲取公開日記", "description": "獲取所有公開的感恩日記列表", "tags": ["Entries"], "parameters": [{"name": "page", "in": "query", "description": "頁碼", "schema": {"type": "integer", "default": 1, "minimum": 1}}, {"name": "limit", "in": "query", "description": "每頁數量", "schema": {"type": "integer", "default": 20, "minimum": 1, "maximum": 100}}], "responses": {"200": {"description": "成功獲取公開日記列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "entries": {"type": "array", "items": {"$ref": "#/components/schemas/Entry"}}}}}}}}}}, "/api/v1/entries": {"get": {"summary": "獲取用戶日記", "description": "獲取當前用戶的日記列表", "tags": ["Entries"], "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "頁碼", "schema": {"type": "integer", "default": 1, "minimum": 1}}, {"name": "limit", "in": "query", "description": "每頁數量", "schema": {"type": "integer", "default": 20, "minimum": 1, "maximum": 100}}], "responses": {"200": {"description": "成功獲取用戶日記列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "entries": {"type": "array", "items": {"$ref": "#/components/schemas/Entry"}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}}, "post": {"summary": "創建日記", "description": "創建新的感恩日記", "tags": ["Entries"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["content"], "properties": {"content": {"type": "string", "description": "日記內容", "example": "今天是美好的一天，感謝生活中的每一個小確幸。"}, "emotion": {"type": "string", "description": "情感標籤", "enum": ["grateful", "joyful", "peaceful", "love", "hopeful", "growth", "sad", "angry"], "default": "grateful", "example": "grateful"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "標籤列表", "example": ["感恩", "家庭"]}, "privacy": {"type": "string", "description": "隱私設置", "enum": ["public", "private", "friends"], "default": "private", "example": "public"}, "is_anonymous": {"type": "boolean", "description": "是否匿名發布", "default": false, "example": false}, "latitude": {"type": "number", "description": "緯度", "example": 25.033}, "longitude": {"type": "number", "description": "經度", "example": 121.5654}, "address": {"type": "string", "description": "地址", "example": "台北市信義區"}}}}}}, "responses": {"201": {"description": "日記創建成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Entry created successfully"}, "entry": {"$ref": "#/components/schemas/Entry"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}, "/api/v1/entries/{id}": {"get": {"summary": "獲取單篇日記", "description": "根據 ID 獲取特定日記的詳細信息", "tags": ["Entries"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "日記 ID", "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "成功獲取日記詳情", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "entry": {"$ref": "#/components/schemas/Entry"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}, "put": {"summary": "更新日記", "description": "更新指定 ID 的日記內容", "tags": ["Entries"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "日記 ID", "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"content": {"type": "string", "description": "日記內容", "example": "更新後的感恩日記內容"}, "emotion": {"type": "string", "description": "情感標籤", "enum": ["grateful", "joyful", "peaceful", "love", "hopeful", "growth", "sad", "angry"], "example": "grateful"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "標籤列表", "example": ["感恩", "更新"]}, "privacy": {"type": "string", "description": "隱私設置", "enum": ["public", "private", "friends"], "example": "public"}}}}}}, "responses": {"200": {"description": "日記更新成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Entry updated successfully"}, "entry": {"$ref": "#/components/schemas/Entry"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}, "delete": {"summary": "刪除日記", "description": "刪除指定 ID 的日記", "tags": ["Entries"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "日記 ID", "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "日記刪除成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Entry deleted successfully"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}}}}, "/api/v1/entries/upload": {"post": {"summary": "上傳日記圖片", "description": "上傳日記相關的圖片文件", "tags": ["Entries"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"image": {"type": "string", "format": "binary", "description": "圖片文件"}, "entry_id": {"type": "integer", "description": "關聯的日記 ID（可選）", "example": 1}}, "required": ["image"]}}}}, "responses": {"201": {"description": "圖片上傳成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Image uploaded successfully"}, "image": {"$ref": "#/components/schemas/Image"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"User": {"type": "object", "properties": {"id": {"type": "integer", "description": "用戶ID", "example": 1}, "uid": {"type": "string", "description": "用戶唯一標識符", "example": "user_64f1a2b3c4d5e6f7_1234"}, "email": {"type": "string", "format": "email", "description": "用戶電子郵件", "example": "<EMAIL>"}, "display_name": {"type": "string", "description": "用戶顯示名稱", "example": "張三"}, "avatar": {"type": "string", "nullable": true, "description": "用戶頭像URL", "example": "https://example.com/avatar.jpg"}, "bio": {"type": "string", "nullable": true, "description": "用戶個人簡介", "example": "熱愛生活的感恩者"}, "language": {"type": "string", "description": "用戶語言偏好", "example": "zh-TW"}, "timezone": {"type": "string", "description": "用戶時區", "example": "Asia/Taipei"}, "theme": {"type": "string", "description": "用戶主題偏好", "example": "light"}, "profile_visible": {"type": "boolean", "description": "個人資料是否公開", "example": true}, "allow_comments": {"type": "boolean", "description": "是否允許評論", "example": true}, "allow_messages": {"type": "boolean", "description": "是否允許私信", "example": true}, "daily_reminder": {"type": "boolean", "description": "是否啟用每日提醒", "example": true}, "focus_reminder": {"type": "boolean", "description": "是否啟用專注提醒", "example": false}, "social_interaction": {"type": "boolean", "description": "是否啟用社交互動", "example": true}, "push_enabled": {"type": "boolean", "description": "是否啟用推送通知", "example": true}, "created_at": {"type": "string", "format": "date-time", "description": "創建時間", "example": "2024-01-15T10:30:00Z"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新時間", "example": "2024-01-15T10:30:00Z"}}}, "UserStats": {"type": "object", "properties": {"total_entries": {"type": "integer", "description": "總日記數量", "example": 45}, "total_likes": {"type": "integer", "description": "總獲讚數", "example": 128}, "total_comments": {"type": "integer", "description": "總評論數", "example": 67}, "focus_sessions": {"type": "integer", "description": "專注會話數", "example": 23}, "focus_minutes": {"type": "integer", "description": "專注總分鐘數", "example": 1150}, "streak_days": {"type": "integer", "description": "連續記錄天數", "example": 7}, "last_entry_date": {"type": "string", "format": "date", "description": "最後記錄日期", "example": "2024-01-15"}}}, "Achievement": {"type": "object", "properties": {"id": {"type": "integer", "description": "成就ID", "example": 1}, "name": {"type": "string", "description": "成就名稱", "example": "初次記錄"}, "description": {"type": "string", "description": "成就描述", "example": "完成第一篇感恩日記"}, "icon": {"type": "string", "description": "成就圖標", "example": "🎉"}, "category": {"type": "string", "description": "成就類別", "example": "milestone"}, "unlocked_at": {"type": "string", "format": "date-time", "description": "解鎖時間", "example": "2024-01-15T10:30:00Z"}}}, "Entry": {"type": "object", "properties": {"id": {"type": "integer", "description": "日記ID", "example": 1}, "user_id": {"type": "integer", "description": "用戶ID", "example": 1}, "content": {"type": "string", "description": "日記內容", "example": "今天是美好的一天，感謝生活中的每一個小確幸。"}, "emotion": {"type": "string", "description": "情感標籤", "enum": ["grateful", "joyful", "peaceful", "love", "hopeful", "growth", "sad", "angry"], "example": "grateful"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "標籤列表", "example": ["感恩", "家庭", "工作"]}, "privacy": {"type": "string", "description": "隱私設置", "enum": ["public", "private", "friends"], "example": "public"}, "is_anonymous": {"type": "boolean", "description": "是否匿名發布", "example": false}, "latitude": {"type": "number", "nullable": true, "description": "緯度", "example": 25.033}, "longitude": {"type": "number", "nullable": true, "description": "經度", "example": 121.5654}, "address": {"type": "string", "nullable": true, "description": "地址", "example": "台北市信義區"}, "likes_count": {"type": "integer", "description": "點讚數", "example": 5}, "comments_count": {"type": "integer", "description": "評論數", "example": 3}, "created_at": {"type": "string", "format": "date-time", "description": "創建時間", "example": "2024-01-15T10:30:00Z"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新時間", "example": "2024-01-15T10:30:00Z"}}}, "Image": {"type": "object", "properties": {"id": {"type": "integer", "description": "圖片 ID", "example": 1}, "filename": {"type": "string", "description": "原始文件名", "example": "sunset.jpg"}, "stored_filename": {"type": "string", "description": "存儲文件名", "example": "20240115_103000_sunset.jpg"}, "file_path": {"type": "string", "description": "文件路徑", "example": "/uploads/images/20240115_103000_sunset.jpg"}, "file_url": {"type": "string", "description": "訪問 URL", "example": "http://localhost:8000/uploads/images/20240115_103000_sunset.jpg"}, "file_size": {"type": "integer", "description": "文件大小（字節）", "example": 1024000}, "mime_type": {"type": "string", "description": "MIME 類型", "example": "image/jpeg"}, "width": {"type": "integer", "nullable": true, "description": "圖片寬度", "example": 1920}, "height": {"type": "integer", "nullable": true, "description": "圖片高度", "example": 1080}, "entry_id": {"type": "integer", "nullable": true, "description": "關聯的日記 ID", "example": 1}, "user_id": {"type": "integer", "description": "上傳用戶 ID", "example": 1}, "created_at": {"type": "string", "format": "date-time", "description": "上傳時間", "example": "2024-01-15T10:30:00Z"}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "error": {"type": "string", "description": "錯誤信息", "example": "Invalid input"}}}}, "responses": {"BadRequest": {"description": "請求參數錯誤", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Missing required field"}}}}, "Unauthorized": {"description": "未授權訪問", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Authentication required"}}}}, "Forbidden": {"description": "禁止訪問", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Access denied"}}}}, "NotFound": {"description": "資源不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"success": false, "error": "Resource not found"}}}}}}, "tags": [{"name": "System", "description": "系統相關端點"}, {"name": "Authentication", "description": "用戶認證相關端點"}, {"name": "Entries", "description": "日記相關端點"}, {"name": "Users", "description": "用戶管理相關端點"}]}