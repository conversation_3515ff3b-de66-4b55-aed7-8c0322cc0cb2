<?php
require_once __DIR__ . '/../Models/Entry.php';
require_once __DIR__ . '/../Models/User.php';

/**
 * 日記控制器
 */
class EntryController {
    private $entryModel;
    private $userModel;

    public function __construct() {
        $this->entryModel = new Entry();
        $this->userModel = new User();
    }

    /**
     * 獲取日記列表
     */
    public function index() {
        try {
            $page = $_GET['page'] ?? 1;
            $limit = $_GET['limit'] ?? 20;
            $type = $_GET['type'] ?? 'user'; // user, public, popular
            $userId = AuthMiddleware::getCurrentUserId();
            
            switch ($type) {
                case 'public':
                    $entries = $this->entryModel->getPublicEntries($page, $limit);
                    break;
                case 'popular':
                    $days = $_GET['days'] ?? 7;
                    $entries = $this->entryModel->getPopularEntries($page, $limit, $days);
                    break;
                case 'user':
                default:
                    if (!$userId) {
                        $this->error('Authentication required', 401);
                        return;
                    }
                    $entries = $this->entryModel->getUserEntries($userId, $page, $limit);
                    break;
            }
            
            $this->success(['entries' => $entries]);
            
        } catch (Exception $e) {
            $this->error('Failed to get entries: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 獲取單個日記
     */
    public function show($id) {
        try {
            $entry = $this->entryModel->find($id);
            
            if (!$entry) {
                $this->error('Entry not found', 404);
                return;
            }
            
            $currentUserId = AuthMiddleware::getCurrentUserId();
            
            // 檢查訪問權限
            if ($entry['privacy'] === 'private' && $entry['user_id'] != $currentUserId) {
                $this->error('Access denied', 403);
                return;
            }
            
            if ($entry['privacy'] === 'friends' && $entry['user_id'] != $currentUserId) {
                // 這裡可以添加朋友關係檢查邏輯
                // 暫時只允許作者訪問
                $this->error('Access denied', 403);
                return;
            }
            
            // 獲取作者信息
            $author = $this->userModel->getProfile($entry['user_id']);
            $entry['author'] = $author;
            
            // 獲取互動統計
            $interactions = $this->entryModel->getEntryInteractions($id);
            $entry['interactions'] = $interactions;
            
            $this->success(['entry' => $entry]);
            
        } catch (Exception $e) {
            $this->error('Failed to get entry: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 創建日記
     */
    public function create() {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            $input = json_decode(file_get_contents('php://input'), true);
            
            // 驗證必填字段
            if (empty($input['content'])) {
                $this->error('Content is required', 400);
                return;
            }
            
            // 檢查今日日記數量限制
            $todayEntries = $this->entryModel->getTodayEntries($userId);
            $maxEntriesPerDay = 10; // 可以從配置中讀取
            
            if (count($todayEntries) >= $maxEntriesPerDay) {
                $this->error("Daily entry limit reached ($maxEntriesPerDay)", 400);
                return;
            }
            
            // 準備數據
            $entryData = [
                'user_id' => $userId,
                'content' => $input['content'],
                'emotion' => $input['emotion'] ?? 'neutral',
                'tags' => isset($input['tags']) ? json_encode($input['tags']) : '[]',
                'privacy' => $input['privacy'] ?? 'private',
                'is_anonymous' => $input['is_anonymous'] ?? false,
                'latitude' => $input['latitude'] ?? null,
                'longitude' => $input['longitude'] ?? null,
                'address' => $input['address'] ?? null
            ];
            
            // 創建日記
            $entry = $this->entryModel->create($entryData);
            
            // 更新用戶統計
            $this->updateUserStats($userId);
            
            $this->success([
                'message' => 'Entry created successfully',
                'entry' => $entry
            ], 201);
            
        } catch (Exception $e) {
            $this->error('Failed to create entry: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新日記
     */
    public function update($id) {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            $entry = $this->entryModel->find($id);
            
            if (!$entry) {
                $this->error('Entry not found', 404);
                return;
            }
            
            // 檢查所有權
            if ($entry['user_id'] != $userId) {
                $this->error('Access denied', 403);
                return;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            // 允許更新的字段
            $allowedFields = [
                'content', 'emotion', 'tags', 'privacy', 'location', 
                'weather', 'mood_score', 'gratitude_score'
            ];
            
            $updateData = [];
            foreach ($allowedFields as $field) {
                if (isset($input[$field])) {
                    if ($field === 'tags') {
                        $updateData[$field] = json_encode($input[$field]);
                    } else {
                        $updateData[$field] = $input[$field];
                    }
                }
            }
            
            if (empty($updateData)) {
                $this->error('No valid fields to update', 400);
                return;
            }
            
            $updatedEntry = $this->entryModel->update($id, $updateData);
            
            $this->success([
                'message' => 'Entry updated successfully',
                'entry' => $updatedEntry
            ]);
            
        } catch (Exception $e) {
            $this->error('Failed to update entry: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 刪除日記
     */
    public function delete($id) {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            $entry = $this->entryModel->find($id);
            
            if (!$entry) {
                $this->error('Entry not found', 404);
                return;
            }
            
            // 檢查所有權
            if ($entry['user_id'] != $userId) {
                $this->error('Access denied', 403);
                return;
            }
            
            $this->entryModel->delete($id);
            
            // 更新用戶統計
            $this->updateUserStats($userId);
            
            $this->success(['message' => 'Entry deleted successfully']);
            
        } catch (Exception $e) {
            $this->error('Failed to delete entry: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 搜索日記
     */
    public function search() {
        try {
            $searchTerm = $_GET['q'] ?? '';
            $page = $_GET['page'] ?? 1;
            $limit = $_GET['limit'] ?? 20;
            $userId = AuthMiddleware::getCurrentUserId();
            $scope = $_GET['scope'] ?? 'user'; // user, public
            
            if (empty($searchTerm)) {
                $this->error('Search term is required', 400);
                return;
            }
            
            if ($scope === 'user' && !$userId) {
                $this->error('Authentication required', 401);
                return;
            }
            
            $searchUserId = ($scope === 'user') ? $userId : null;
            $results = $this->entryModel->searchEntries($searchTerm, $searchUserId, $page, $limit);
            
            $this->success(['results' => $results]);
            
        } catch (Exception $e) {
            $this->error('Search failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 獲取統計數據
     */
    public function stats() {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            $days = $_GET['days'] ?? 30;
            
            $entryStats = $this->entryModel->getEntryStats($userId, $days);
            $emotionStats = $this->entryModel->getEmotionStats($userId, $days);
            $streak = $this->entryModel->getUserStreak($userId);
            
            $this->success([
                'stats' => $entryStats,
                'emotions' => $emotionStats,
                'streak' => $streak
            ]);
            
        } catch (Exception $e) {
            $this->error('Failed to get stats: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新用戶統計
     */
    private function updateUserStats($userId) {
        try {
            $entryStats = $this->entryModel->getEntryStats($userId, 365);
            $streak = $this->entryModel->getUserStreak($userId);
            
            $this->userModel->updateStats($userId, [
                'total_entries' => $entryStats['total_entries'],
                'streak_days' => $streak,
                'last_entry_date' => date('Y-m-d')
            ]);
        } catch (Exception $e) {
            // 記錄錯誤但不影響主要操作
            error_log('Failed to update user stats: ' . $e->getMessage());
        }
    }

    /**
     * 成功響應
     */
    private function success($data, $code = 200) {
        http_response_code($code);
        header('Content-Type: application/json');
        echo json_encode(array_merge(['success' => true], $data));
    }

    /**
     * 錯誤響應
     */
    private function error($message, $code = 400) {
        http_response_code($code);
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'error' => $message
        ]);
    }
}
