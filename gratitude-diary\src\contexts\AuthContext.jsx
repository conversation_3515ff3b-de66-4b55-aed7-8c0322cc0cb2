/**
 * 認證上下文
 * 提供全局的認證狀態管理
 */

import React, { createContext, useContext } from 'react';
import { useAuth } from '../hooks/useAuth.js';

// 創建認證上下文
const AuthContext = createContext(null);

// 認證提供者組件
export const AuthProvider = ({ children }) => {
  const auth = useAuth();

  return (
    <AuthContext.Provider value={auth}>
      {children}
    </AuthContext.Provider>
  );
};

// 使用認證上下文的 Hook
export const useAuthContext = () => {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  
  return context;
};

// 默認導出
export default AuthContext;
