# 感恩日記API - 部署執行步驟

## 🚀 快速部署指南

### 第一步：環境準備

#### 1.1 安裝LAMP環境
```bash
# Ubuntu/Debian系統
sudo apt update && sudo apt upgrade -y

# 安裝Apache
sudo apt install -y apache2

# 安裝MySQL
sudo apt install -y mysql-server
sudo mysql_secure_installation

# 安裝PHP 8.2
sudo add-apt-repository ppa:ondrej/php
sudo apt update
sudo apt install -y php8.2 php8.2-fpm php8.2-mysql php8.2-curl php8.2-gd php8.2-mbstring php8.2-xml php8.2-zip php8.2-json php8.2-bcmath

# 安裝Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# 啟用Apache模組
sudo a2enmod rewrite
sudo a2enmod ssl
sudo systemctl restart apache2
```

#### 1.2 創建專案目錄
```bash
# 創建專案目錄
sudo mkdir -p /var/www/gratitude-diary-api
sudo chown -R $USER:www-data /var/www/gratitude-diary-api
cd /var/www/gratitude-diary-api

# 創建目錄結構
mkdir -p {app/{Controllers,Models,Services,Middleware},config,routes,public,storage/{logs,uploads},database/{migrations,seeds}}
```

### 第二步：下載和配置代碼

#### 2.1 複製已生成的文件
```bash
# 將我生成的文件複製到對應目錄
# database/migrations/001_create_tables.sql
# database/seeds/initial_data.sql  
# app/Services/Database.php
# app/Services/AuthService.php
```

#### 2.2 創建配置文件
```bash
# 創建環境配置文件
nano .env
```

```bash
# .env 內容
APP_NAME="Gratitude Diary API"
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost/gratitude-diary-api

# 數據庫配置
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=gratitude_diary
DB_USERNAME=gratitude_user
DB_PASSWORD=your_secure_password_here

# Firebase配置
FIREBASE_PROJECT_ID=your-project-id
FCM_SERVER_KEY=your-fcm-server-key
FCM_SENDER_ID=your-sender-id
FIREBASE_API_KEY=your-api-key

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
JWT_EXPIRE=86400

# 文件上傳配置
UPLOAD_PATH=storage/uploads
MAX_FILE_SIZE=10485760
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,mp3,wav,m4a
```

#### 2.3 創建數據庫配置
```bash
nano config/database.php
```

```php
<?php
return [
    'host' => $_ENV['DB_HOST'] ?? 'localhost',
    'port' => $_ENV['DB_PORT'] ?? 3306,
    'database' => $_ENV['DB_DATABASE'] ?? 'gratitude_diary',
    'username' => $_ENV['DB_USERNAME'] ?? 'root',
    'password' => $_ENV['DB_PASSWORD'] ?? '',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'options' => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]
];
```

#### 2.4 安裝PHP依賴
```bash
# 創建composer.json
nano composer.json
```

```json
{
    "name": "gratitude-diary/api",
    "description": "Gratitude Diary API",
    "type": "project",
    "require": {
        "php": ">=8.1",
        "firebase/php-jwt": "^6.0",
        "vlucas/phpdotenv": "^5.0",
        "monolog/monolog": "^3.0",
        "intervention/image": "^2.7",
        "phpmailer/phpmailer": "^6.8"
    },
    "require-dev": {
        "phpunit/phpunit": "^10.0"
    },
    "autoload": {
        "psr-4": {
            "App\\": "app/"
        }
    }
}
```

```bash
# 安裝依賴
composer install
```

### 第三步：數據庫設置

#### 3.1 創建數據庫和用戶
```bash
# 登入MySQL
sudo mysql -u root -p

# 執行以下SQL命令
```

```sql
-- 創建數據庫
CREATE DATABASE gratitude_diary CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 創建用戶
CREATE USER 'gratitude_user'@'localhost' IDENTIFIED BY 'your_secure_password_here';

-- 授權
GRANT ALL PRIVILEGES ON gratitude_diary.* TO 'gratitude_user'@'localhost';
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

#### 3.2 導入數據庫結構
```bash
# 導入表結構
mysql -u gratitude_user -p gratitude_diary < database/migrations/001_create_tables.sql

# 導入初始數據
mysql -u gratitude_user -p gratitude_diary < database/seeds/initial_data.sql
```

#### 3.3 驗證數據庫
```bash
# 檢查表是否創建成功
mysql -u gratitude_user -p gratitude_diary -e "SHOW TABLES;"

# 檢查示例數據
mysql -u gratitude_user -p gratitude_diary -e "SELECT COUNT(*) as user_count FROM users;"
mysql -u gratitude_user -p gratitude_diary -e "SELECT COUNT(*) as entry_count FROM entries;"
```

### 第四步：Apache配置

#### 4.1 創建虛擬主機
```bash
sudo nano /etc/apache2/sites-available/gratitude-diary-api.conf
```

```apache
<VirtualHost *:80>
    ServerName localhost
    DocumentRoot /var/www/gratitude-diary-api/public
    
    <Directory /var/www/gratitude-diary-api/public>
        AllowOverride All
        Require all granted
        
        # 處理CORS
        Header always set Access-Control-Allow-Origin "*"
        Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
        Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
    </Directory>
    
    # 隱藏敏感文件
    <Files ".env">
        Require all denied
    </Files>
    
    ErrorLog ${APACHE_LOG_DIR}/gratitude-diary-api_error.log
    CustomLog ${APACHE_LOG_DIR}/gratitude-diary-api_access.log combined
</VirtualHost>
```

#### 4.2 啟用站點
```bash
# 啟用站點
sudo a2ensite gratitude-diary-api.conf

# 啟用headers模組
sudo a2enmod headers

# 重啟Apache
sudo systemctl restart apache2

# 檢查配置
sudo apache2ctl configtest
```

### 第五步：創建基本API文件

#### 5.1 創建入口文件
```bash
nano public/index.php
```

```php
<?php
require_once '../vendor/autoload.php';

// 載入環境變數
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// 設置錯誤報告
if ($_ENV['APP_DEBUG'] === 'true') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// 設置時區
date_default_timezone_set('Asia/Taipei');

// CORS處理
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 載入服務
require_once '../app/Services/Database.php';
require_once '../app/Services/AuthService.php';

// 簡單的健康檢查端點
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$path = str_replace('/gratitude-diary-api/public', '', $path);

if ($path === '/health' || $path === '/api/v1/health') {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => 'API is running',
        'timestamp' => date('Y-m-d H:i:s'),
        'version' => '1.0.0'
    ]);
    exit();
}

// 測試數據庫連接
if ($path === '/test-db' || $path === '/api/v1/test-db') {
    header('Content-Type: application/json');
    try {
        $db = Database::getInstance();
        $result = $db->fetchOne("SELECT COUNT(*) as count FROM users");
        echo json_encode([
            'success' => true,
            'message' => 'Database connection successful',
            'user_count' => $result['count']
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Database connection failed',
            'error' => $e->getMessage()
        ]);
    }
    exit();
}

// 默認響應
header('Content-Type: application/json');
echo json_encode([
    'success' => true,
    'message' => 'Gratitude Diary API',
    'version' => '1.0.0',
    'endpoints' => [
        'health' => '/api/v1/health',
        'test-db' => '/api/v1/test-db'
    ]
]);
```

#### 5.2 創建.htaccess文件
```bash
nano public/.htaccess
```

```apache
RewriteEngine On

# 處理CORS預檢請求
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# 將所有請求重定向到index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# 安全設置
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

# 設置文件上傳限制
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
```

### 第六步：設置權限

```bash
# 設置目錄權限
sudo chown -R www-data:www-data /var/www/gratitude-diary-api/storage/
sudo chmod -R 775 /var/www/gratitude-diary-api/storage/
sudo chmod -R 755 /var/www/gratitude-diary-api/public/

# 創建日誌目錄
mkdir -p storage/logs
touch storage/logs/auth.log
sudo chown -R www-data:www-data storage/logs/
sudo chmod -R 775 storage/logs/
```

### 第七步：測試API

#### 7.1 測試基本連接
```bash
# 測試API健康狀態
curl http://localhost/gratitude-diary-api/public/health

# 測試數據庫連接
curl http://localhost/gratitude-diary-api/public/test-db
```

#### 7.2 預期響應
```json
// 健康檢查響應
{
    "success": true,
    "message": "API is running",
    "timestamp": "2024-01-01 12:00:00",
    "version": "1.0.0"
}

// 數據庫測試響應
{
    "success": true,
    "message": "Database connection successful",
    "user_count": "3"
}
```

### 第八步：故障排除

#### 8.1 常見問題檢查
```bash
# 檢查Apache狀態
sudo systemctl status apache2

# 檢查MySQL狀態
sudo systemctl status mysql

# 檢查PHP版本
php -v

# 檢查Apache錯誤日誌
sudo tail -f /var/log/apache2/gratitude-diary-api_error.log

# 檢查PHP錯誤日誌
sudo tail -f /var/log/apache2/error.log
```

#### 8.2 權限問題修復
```bash
# 如果遇到權限問題
sudo chown -R www-data:www-data /var/www/gratitude-diary-api/
sudo chmod -R 755 /var/www/gratitude-diary-api/
sudo chmod -R 775 /var/www/gratitude-diary-api/storage/
```

#### 8.3 數據庫連接問題
```bash
# 測試數據庫連接
mysql -u gratitude_user -p gratitude_diary -e "SELECT 1;"

# 檢查用戶權限
mysql -u root -p -e "SHOW GRANTS FOR 'gratitude_user'@'localhost';"
```

## ✅ 完成檢查清單

- [ ] LAMP環境安裝完成
- [ ] 專案目錄創建完成
- [ ] 環境配置文件設置完成
- [ ] 數據庫創建和導入完成
- [ ] Apache虛擬主機配置完成
- [ ] 基本API文件創建完成
- [ ] 權限設置完成
- [ ] API健康檢查通過
- [ ] 數據庫連接測試通過

完成以上步驟後，您的基礎API環境就搭建完成了！接下來我可以繼續為您生成完整的控制器和路由代碼。
