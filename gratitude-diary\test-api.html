<!DOCTYPE html>
<html>
<head>
    <title>API 測試</title>
</head>
<body>
    <h1>API 測試</h1>
    <button onclick="testHealthCheck()">測試健康檢查</button>
    <button onclick="testRegister()">測試註冊</button>
    <div id="result"></div>

    <script>
        const API_BASE_URL = 'http://localhost/diary/app/api-server/public/index.php';

        async function testHealthCheck() {
            try {
                const url = `${API_BASE_URL}?path=${encodeURIComponent('/api/v1/health')}`;
                console.log('Health check URL:', url);
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                console.log('Health check response:', data);
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                console.error('Health check error:', error);
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }

        async function testRegister() {
            try {
                const url = `${API_BASE_URL}?path=${encodeURIComponent('/api/v1/auth/register')}`;
                console.log('Register URL:', url);
                
                const userData = {
                    email: '<EMAIL>',
                    password: 'password123',
                    display_name: '前端測試用戶'
                };
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(userData)
                });
                
                const data = await response.json();
                console.log('Register response:', data);
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                console.error('Register error:', error);
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
