<?php
/**
 * 認證中間件
 * 驗證JWT token並設置當前用戶
 */
class AuthMiddleware {
    
    /**
     * 處理請求
     */
    public static function handle() {
        // 初始化認證服務
        AuthService::init();
        
        // 獲取Authorization header
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        
        if (empty($authHeader)) {
            self::unauthorized('Missing authorization header');
            return false;
        }
        
        // 檢查Bearer token格式
        if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            self::unauthorized('Invalid authorization header format');
            return false;
        }
        
        $token = $matches[1];
        
        try {
            // 驗證token
            $payload = AuthService::verifyToken($token);
            
            if (!$payload) {
                self::unauthorized('Invalid token');
                return false;
            }
            
            // 檢查用戶是否存在
            $userModel = new User();
            $user = $userModel->find($payload['user_id']);
            
            if (!$user) {
                self::unauthorized('User not found');
                return false;
            }
            
            // 設置全局用戶信息
            $GLOBALS['current_user'] = $user;
            $GLOBALS['current_user_id'] = $user['id'];
            
            return true;
            
        } catch (Exception $e) {
            self::unauthorized('Token verification failed: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 可選認證 - 如果有token則驗證，沒有則繼續
     */
    public static function optional() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        
        if (empty($authHeader)) {
            return true; // 沒有token，繼續執行
        }
        
        return self::handle(); // 有token，進行驗證
    }
    
    /**
     * 獲取當前用戶
     */
    public static function getCurrentUser() {
        return $GLOBALS['current_user'] ?? null;
    }
    
    /**
     * 獲取當前用戶ID
     */
    public static function getCurrentUserId() {
        return $GLOBALS['current_user_id'] ?? null;
    }
    
    /**
     * 檢查當前用戶是否為指定用戶
     */
    public static function isCurrentUser($userId) {
        return self::getCurrentUserId() == $userId;
    }
    
    /**
     * 檢查當前用戶是否為管理員
     */
    public static function isAdmin() {
        $user = self::getCurrentUser();
        return $user && ($user['role'] ?? '') === 'admin';
    }
    
    /**
     * 返回未授權響應
     */
    private static function unauthorized($message = 'Unauthorized') {
        http_response_code(401);
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'error' => 'Unauthorized',
            'message' => $message
        ]);
        exit();
    }
    
    /**
     * 檢查用戶權限
     */
    public static function checkPermission($permission) {
        $user = self::getCurrentUser();
        
        if (!$user) {
            self::unauthorized('Authentication required');
            return false;
        }
        
        // 管理員擁有所有權限
        if (self::isAdmin()) {
            return true;
        }
        
        // 這裡可以實現更複雜的權限檢查邏輯
        // 目前簡單返回true，表示已認證用戶擁有基本權限
        return true;
    }
    
    /**
     * 檢查資源所有權
     */
    public static function checkOwnership($resourceUserId) {
        $currentUserId = self::getCurrentUserId();
        
        if (!$currentUserId) {
            self::unauthorized('Authentication required');
            return false;
        }
        
        // 管理員可以訪問所有資源
        if (self::isAdmin()) {
            return true;
        }
        
        // 檢查是否為資源所有者
        if ($currentUserId != $resourceUserId) {
            http_response_code(403);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => 'Forbidden',
                'message' => 'You do not have permission to access this resource'
            ]);
            exit();
        }
        
        return true;
    }
    
    /**
     * 刷新用戶活動時間
     */
    public static function updateUserActivity() {
        $userId = self::getCurrentUserId();
        if ($userId) {
            $userModel = new User();
            $userModel->updateLastActivity($userId);
        }
    }
}
