"use client";
import "./chunk-C6WWHQR7.js";
import {
  createSvgIcon
} from "./chunk-QVI57TQV.js";
import "./chunk-YZ6PAUYO.js";
import "./chunk-BRYCWNNY.js";
import "./chunk-6YD57FQ6.js";
import {
  require_jsx_runtime
} from "./chunk-3NBMPMSA.js";
import {
  __toESM
} from "./chunk-DAFPDBRK.js";

// node_modules/@mui/icons-material/esm/PhotoLibrary.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var PhotoLibrary_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "M22 16V4c0-1.1-.9-2-2-2H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2m-11-4 2.03 2.71L16 11l4 5H8zM2 6v14c0 1.1.9 2 2 2h14v-2H4V6z"
}), "PhotoLibrary");
export {
  PhotoLibrary_default as default
};
//# sourceMappingURL=@mui_icons-material_PhotoLibrary.js.map
