# 前端整合指南

## 概述

本指南說明如何將現有的React前端應用與Firebase後端API進行整合，實現完整的感恩日記應用功能。

## 技術架構

### 前端技術棧
- **React 19.1.0**: 前端框架
- **Material-UI 7.1.0**: UI組件庫
- **React Router DOM 7.6.1**: 路由管理
- **Framer Motion 12.15.0**: 動畫效果
- **React Hook Form 7.57.0**: 表單處理
- **Firebase SDK**: 後端服務整合

### 新增依賴
```bash
npm install firebase
npm install @firebase/auth @firebase/firestore @firebase/storage
npm install react-query @tanstack/react-query
npm install axios
npm install react-hot-toast
```

## Firebase SDK 配置

### 1. Firebase配置文件
```javascript
// src/config/firebase.js
import { initializeApp } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getStorage, connectStorageEmulator } from 'firebase/storage';
import { getFunctions, connectFunctionsEmulator } from 'firebase/functions';

const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.REACT_APP_FIREBASE_APP_ID
};

// 初始化Firebase
const app = initializeApp(firebaseConfig);

// 初始化服務
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);
export const functions = getFunctions(app, 'asia-east1');

// 開發環境連接模擬器
if (process.env.NODE_ENV === 'development') {
  connectAuthEmulator(auth, 'http://localhost:9099');
  connectFirestoreEmulator(db, 'localhost', 8080);
  connectStorageEmulator(storage, 'localhost', 9199);
  connectFunctionsEmulator(functions, 'localhost', 5001);
}
```

### 2. 環境變數配置
```bash
# .env
REACT_APP_FIREBASE_API_KEY=your-api-key
REACT_APP_FIREBASE_AUTH_DOMAIN=gratitude-diary.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=gratitude-diary
REACT_APP_FIREBASE_STORAGE_BUCKET=gratitude-diary.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=123456789
REACT_APP_FIREBASE_APP_ID=your-app-id
REACT_APP_API_BASE_URL=https://asia-east1-gratitude-diary.cloudfunctions.net/api
```

## 認證系統整合

### 1. 認證Context
```javascript
// src/contexts/AuthContext.jsx
import React, { createContext, useContext, useEffect, useState } from 'react';
import { 
  onAuthStateChanged, 
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  updateProfile
} from 'firebase/auth';
import { auth } from '../config/firebase';
import { apiClient } from '../services/api';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [token, setToken] = useState(null);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        const idToken = await firebaseUser.getIdToken();
        setToken(idToken);
        setUser(firebaseUser);
        
        // 設置API客戶端的認證token
        apiClient.defaults.headers.common['Authorization'] = `Bearer ${idToken}`;
      } else {
        setUser(null);
        setToken(null);
        delete apiClient.defaults.headers.common['Authorization'];
      }
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const login = async (email, password) => {
    try {
      const result = await signInWithEmailAndPassword(auth, email, password);
      return { success: true, user: result.user };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const register = async (email, password, displayName) => {
    try {
      const result = await createUserWithEmailAndPassword(auth, email, password);
      await updateProfile(result.user, { displayName });
      
      // 調用後端API創建用戶資料
      await apiClient.post('/auth/register', {
        uid: result.user.uid,
        email,
        displayName
      });
      
      return { success: true, user: result.user };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const logout = async () => {
    try {
      await signOut(auth);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const value = {
    user,
    token,
    loading,
    login,
    register,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
```

### 2. API客戶端配置
```javascript
// src/services/api.js
import axios from 'axios';
import toast from 'react-hot-toast';

export const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 請求攔截器
apiClient.interceptors.request.use(
  (config) => {
    // 可以在這裡添加loading狀態
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 響應攔截器
apiClient.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    const message = error.response?.data?.error?.message || '請求失敗';
    toast.error(message);
    
    // 處理認證錯誤
    if (error.response?.status === 401) {
      // 重定向到登入頁面
      window.location.href = '/login';
    }
    
    return Promise.reject(error);
  }
);
```

## 數據管理整合

### 1. React Query配置
```javascript
// src/config/queryClient.js
import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5分鐘
    },
    mutations: {
      retry: 1,
    },
  },
});
```

### 2. 日記服務
```javascript
// src/services/entryService.js
import { apiClient } from './api';

export const entryService = {
  // 獲取日記列表
  getEntries: async (params = {}) => {
    const response = await apiClient.get('/entries', { params });
    return response.data;
  },

  // 創建日記
  createEntry: async (entryData) => {
    const response = await apiClient.post('/entries', entryData);
    return response.data;
  },

  // 更新日記
  updateEntry: async (entryId, entryData) => {
    const response = await apiClient.put(`/entries/${entryId}`, entryData);
    return response.data;
  },

  // 刪除日記
  deleteEntry: async (entryId) => {
    const response = await apiClient.delete(`/entries/${entryId}`);
    return response.data;
  },

  // 點讚日記
  likeEntry: async (entryId) => {
    const response = await apiClient.post(`/entries/${entryId}/like`);
    return response.data;
  },

  // 添加評論
  addComment: async (entryId, content, parentId = null) => {
    const response = await apiClient.post(`/entries/${entryId}/comments`, {
      content,
      parentId
    });
    return response.data;
  },

  // 上傳媒體
  uploadMedia: async (entryId, file, type) => {
    const formData = new FormData();
    formData.append('media', file);
    formData.append('type', type);

    const response = await apiClient.post(`/entries/${entryId}/media`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }
};
```

### 3. 用戶服務
```javascript
// src/services/userService.js
import { apiClient } from './api';

export const userService = {
  // 獲取用戶資料
  getProfile: async () => {
    const response = await apiClient.get('/users/profile');
    return response.data;
  },

  // 更新用戶資料
  updateProfile: async (profileData) => {
    const response = await apiClient.put('/users/profile', profileData);
    return response.data;
  },

  // 獲取用戶統計
  getStats: async (period = 'month') => {
    const response = await apiClient.get('/users/stats', {
      params: { period }
    });
    return response.data;
  },

  // 上傳頭像
  uploadAvatar: async (file) => {
    const formData = new FormData();
    formData.append('avatar', file);

    const response = await apiClient.post('/users/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }
};
```

## 自定義Hooks

### 1. 日記相關Hooks
```javascript
// src/hooks/useEntries.js
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { entryService } from '../services/entryService';
import toast from 'react-hot-toast';

export const useEntries = (params = {}) => {
  return useQuery({
    queryKey: ['entries', params],
    queryFn: () => entryService.getEntries(params),
  });
};

export const useCreateEntry = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: entryService.createEntry,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['entries'] });
      toast.success('日記創建成功！');
    },
    onError: (error) => {
      toast.error('創建日記失敗');
    },
  });
};

export const useLikeEntry = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: entryService.likeEntry,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['entries'] });
    },
  });
};
```

### 2. 用戶相關Hooks
```javascript
// src/hooks/useUser.js
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { userService } from '../services/userService';
import toast from 'react-hot-toast';

export const useUserProfile = () => {
  return useQuery({
    queryKey: ['user', 'profile'],
    queryFn: userService.getProfile,
  });
};

export const useUserStats = (period = 'month') => {
  return useQuery({
    queryKey: ['user', 'stats', period],
    queryFn: () => userService.getStats(period),
  });
};

export const useUpdateProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: userService.updateProfile,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user'] });
      toast.success('個人資料更新成功！');
    },
  });
};
```

## 頁面組件更新

### 1. 首頁更新
```javascript
// src/pages/HomePage.jsx (更新部分)
import { useEntries, useLikeEntry } from '../hooks/useEntries';

const HomePage = () => {
  const [selectedEmotion, setSelectedEmotion] = useState('all');
  const { data: entriesData, isLoading } = useEntries({
    emotion: selectedEmotion !== 'all' ? selectedEmotion : undefined,
    page: 1,
    limit: 20
  });
  const likeMutation = useLikeEntry();

  const handleLike = (entryId) => {
    likeMutation.mutate(entryId);
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  const entries = entriesData?.entries || [];

  // 渲染邏輯保持不變，但使用真實數據
  return (
    // ... 現有的JSX結構
  );
};
```

### 2. 創建日記頁面更新
```javascript
// src/pages/CreateDiaryPage.jsx (更新部分)
import { useCreateEntry } from '../hooks/useEntries';
import { useForm } from 'react-hook-form';

const CreateDiaryPage = () => {
  const navigate = useNavigate();
  const createEntryMutation = useCreateEntry();
  const { register, handleSubmit, watch, setValue } = useForm();

  const onSubmit = async (data) => {
    try {
      await createEntryMutation.mutateAsync({
        content: data.content,
        emotion: data.emotion,
        tags: data.tags || [],
        privacy: data.privacy || 'public',
        isAnonymous: data.isAnonymous || false
      });
      navigate('/app');
    } catch (error) {
      console.error('創建日記失敗:', error);
    }
  };

  // 其餘組件邏輯保持不變
};
```

### 3. 個人中心頁面更新
```javascript
// src/pages/ProfilePage.jsx (更新部分)
import { useUserProfile, useUserStats } from '../hooks/useUser';

const ProfilePage = () => {
  const { data: profile } = useUserProfile();
  const { data: stats } = useUserStats('month');

  const userStats = {
    name: profile?.displayName || '用戶',
    gratitudeDays: stats?.streakDays || 0,
    level: '感恩新手',
    thisMonthEntries: stats?.totalEntries || 0,
    thisMonthLikes: stats?.totalLikes || 0,
    focusSessions: stats?.focusSessions || 0,
    focusMinutes: stats?.focusMinutes || 0,
  };

  // 其餘組件邏輯保持不變
};
```

## 錯誤處理與Loading狀態

### 1. 全局錯誤邊界
```javascript
// src/components/ErrorBoundary.jsx
import React from 'react';
import { Box, Typography, Button } from '@mui/material';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('錯誤邊界捕獲錯誤:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <Box sx={{ 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center', 
          justifyContent: 'center',
          minHeight: '100vh',
          p: 3
        }}>
          <Typography variant="h5" sx={{ mb: 2 }}>
            哎呀，出現了一些問題
          </Typography>
          <Typography variant="body1" sx={{ mb: 3, textAlign: 'center' }}>
            我們正在努力修復這個問題，請稍後再試。
          </Typography>
          <Button 
            variant="contained" 
            onClick={() => window.location.reload()}
          >
            重新載入
          </Button>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
```

### 2. Loading組件
```javascript
// src/components/LoadingSpinner.jsx
import React from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';

const LoadingSpinner = ({ message = '載入中...' }) => {
  return (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '200px',
      gap: 2
    }}>
      <CircularProgress size={40} />
      <Typography variant="body2" color="text.secondary">
        {message}
      </Typography>
    </Box>
  );
};

export default LoadingSpinner;
```

## 應用主入口更新

### 1. App.jsx更新
```javascript
// src/App.jsx (更新部分)
import React from 'react';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from './contexts/AuthContext';
import { queryClient } from './config/queryClient';
import ErrorBoundary from './components/ErrorBoundary';

function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <ThemeProvider theme={theme}>
            <CssBaseline />
            <Router>
              {/* 現有的路由結構 */}
            </Router>
            <Toaster
              position="top-center"
              toastOptions={{
                duration: 3000,
                style: {
                  background: '#363636',
                  color: '#fff',
                },
              }}
            />
          </ThemeProvider>
        </AuthProvider>
        <ReactQueryDevtools initialIsOpen={false} />
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
```

### 2. 路由保護
```javascript
// src/components/ProtectedRoute.jsx
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from './LoadingSpinner';

const ProtectedRoute = ({ children }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return <LoadingSpinner message="驗證用戶身份..." />;
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  return children;
};

export default ProtectedRoute;
```

## 專注工作功能整合

### 1. 專注工作服務
```javascript
// src/services/focusService.js
import { apiClient } from './api';

export const focusService = {
  // 開始專注工作
  startSession: async (sessionData) => {
    const response = await apiClient.post('/focus/start', sessionData);
    return response.data;
  },

  // 完成專注工作
  completeSession: async (sessionId, completionData) => {
    const response = await apiClient.post(`/focus/${sessionId}/complete`, completionData);
    return response.data;
  },

  // 獲取專注統計
  getStats: async (period = 'month') => {
    const response = await apiClient.get('/focus/stats', {
      params: { period }
    });
    return response.data;
  }
};
```

### 2. 專注工作Hooks
```javascript
// src/hooks/useFocus.js
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { focusService } from '../services/focusService';
import toast from 'react-hot-toast';

export const useFocusStats = (period = 'month') => {
  return useQuery({
    queryKey: ['focus', 'stats', period],
    queryFn: () => focusService.getStats(period),
  });
};

export const useStartFocusSession = () => {
  return useMutation({
    mutationFn: focusService.startSession,
    onSuccess: () => {
      toast.success('專注工作開始！');
    },
  });
};

export const useCompleteFocusSession = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ sessionId, completionData }) =>
      focusService.completeSession(sessionId, completionData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['focus'] });
      toast.success('專注工作完成！');
    },
  });
};
```

## 離線支援

### 1. Service Worker配置
```javascript
// public/sw.js
const CACHE_NAME = 'gratitude-diary-v1';
const urlsToCache = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/manifest.json'
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        if (response) {
          return response;
        }
        return fetch(event.request);
      })
  );
});
```

### 2. 離線數據同步
```javascript
// src/hooks/useOfflineSync.js
import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';

export const useOfflineSync = () => {
  const queryClient = useQueryClient();

  useEffect(() => {
    const handleOnline = () => {
      // 重新獲取所有查詢
      queryClient.invalidateQueries();
    };

    const handleOffline = () => {
      // 處理離線狀態
      console.log('應用已離線');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [queryClient]);
};
```

## 性能優化

### 1. 圖片懶加載
```javascript
// src/components/LazyImage.jsx
import React, { useState, useRef, useEffect } from 'react';
import { Box, Skeleton } from '@mui/material';

const LazyImage = ({ src, alt, ...props }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef();

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <Box ref={imgRef} {...props}>
      {isInView && (
        <>
          {!isLoaded && (
            <Skeleton variant="rectangular" width="100%" height="100%" />
          )}
          <img
            src={src}
            alt={alt}
            onLoad={() => setIsLoaded(true)}
            style={{
              display: isLoaded ? 'block' : 'none',
              width: '100%',
              height: '100%',
              objectFit: 'cover'
            }}
          />
        </>
      )}
    </Box>
  );
};

export default LazyImage;
```

### 2. 虛擬滾動
```javascript
// src/components/VirtualList.jsx
import React, { useMemo } from 'react';
import { FixedSizeList as List } from 'react-window';
import { Box } from '@mui/material';

const VirtualList = ({ items, itemHeight, renderItem, height = 400 }) => {
  const Row = ({ index, style }) => (
    <div style={style}>
      {renderItem(items[index], index)}
    </div>
  );

  return (
    <Box sx={{ height }}>
      <List
        height={height}
        itemCount={items.length}
        itemSize={itemHeight}
        width="100%"
      >
        {Row}
      </List>
    </Box>
  );
};

export default VirtualList;
```

## 部署配置

### 1. 建置腳本更新
```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "build:staging": "REACT_APP_ENV=staging vite build",
    "build:production": "REACT_APP_ENV=production vite build",
    "preview": "vite preview",
    "deploy:staging": "npm run build:staging && firebase deploy --only hosting:staging",
    "deploy:production": "npm run build:production && firebase deploy --only hosting:production"
  }
}
```

### 2. 環境配置
```javascript
// src/config/environment.js
const environments = {
  development: {
    apiBaseUrl: 'http://localhost:5001/gratitude-diary/asia-east1/api',
    enableDevtools: true,
  },
  staging: {
    apiBaseUrl: 'https://asia-east1-gratitude-diary-staging.cloudfunctions.net/api',
    enableDevtools: true,
  },
  production: {
    apiBaseUrl: 'https://asia-east1-gratitude-diary.cloudfunctions.net/api',
    enableDevtools: false,
  }
};

export const config = environments[process.env.REACT_APP_ENV || 'development'];
```

## 測試配置

### 1. 測試工具設定
```bash
npm install --save-dev @testing-library/react @testing-library/jest-dom
npm install --save-dev @testing-library/user-event
npm install --save-dev msw
```

### 2. API Mock設定
```javascript
// src/mocks/handlers.js
import { rest } from 'msw';

export const handlers = [
  rest.get('/api/entries', (req, res, ctx) => {
    return res(
      ctx.json({
        success: true,
        data: {
          entries: [
            // 模擬數據
          ]
        }
      })
    );
  }),

  rest.post('/api/entries', (req, res, ctx) => {
    return res(
      ctx.json({
        success: true,
        data: {
          entry: {
            id: 'new-entry-id',
            ...req.body
          }
        }
      })
    );
  }),
];
```

這個前端整合指南提供了完整的Firebase整合方案，包括認證、數據管理、錯誤處理、性能優化等各個方面，確保應用具備生產環境的穩定性和可擴展性。
```
