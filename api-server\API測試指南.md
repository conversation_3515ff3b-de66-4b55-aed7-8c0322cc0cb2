# 感恩日記 API 測試指南

## 🎉 API 已完成！

您的感恩日記 API 已經完全構建完成並可以使用了！

## 📋 已完成的功能

### ✅ 核心組件
- **數據庫連接** - Database.php (單例模式，支持事務)
- **認證服務** - AuthService.php (JWT token 生成和驗證)
- **用戶模型** - User.php (用戶管理、統計、成就)
- **日記模型** - Entry.php (日記 CRUD、搜索、統計)
- **認證中間件** - AuthMiddleware.php (JWT 驗證)
- **CORS 中間件** - CorsMiddleware.php (跨域支持)

### ✅ 控制器
- **認證控制器** - AuthController.php (註冊、登入、用戶資料)
- **日記控制器** - EntryController.php (日記 CRUD、搜索、統計)

### ✅ 路由系統
- **完整路由** - routes/api.php (RESTful API 端點)
- **中間件支持** - 認證保護和 CORS 處理

## 🚀 API 端點列表

### 公開端點 (無需認證)
```
GET  /api/v1/health          - 健康檢查
GET  /api/v1/test-db         - 數據庫連接測試
POST /api/v1/auth/register   - 用戶註冊
POST /api/v1/auth/login      - 用戶登入
POST /api/v1/auth/logout     - 用戶登出
GET  /api/v1/entries/public  - 獲取公開日記
GET  /api/v1/entries/popular - 獲取熱門日記
```

### 需要認證的端點
```
GET  /api/v1/auth/me         - 獲取當前用戶信息
PUT  /api/v1/auth/profile    - 更新用戶資料
PUT  /api/v1/auth/password   - 更改密碼

GET  /api/v1/entries         - 獲取用戶日記列表
POST /api/v1/entries         - 創建新日記
GET  /api/v1/entries/{id}    - 獲取單個日記
PUT  /api/v1/entries/{id}    - 更新日記
DELETE /api/v1/entries/{id}  - 刪除日記

GET  /api/v1/entries/search  - 搜索日記
GET  /api/v1/stats           - 獲取統計數據
```

## 🧪 測試方法

### 方法1：使用 WAMP 服務器
1. 確保 WAMP 服務器運行
2. 訪問：`http://localhost/diary/app/api-server/public/api/v1/health`
3. 應該看到：`{"success":true,"message":"API is running",...}`

### 方法2：使用 PHP 內建服務器
```powershell
cd api-server/public
php -S localhost:8000
```
然後訪問：`http://localhost:8000/api/v1/health`

### 方法3：使用 Postman 或 curl

#### 測試健康檢查
```bash
curl http://localhost/diary/app/api-server/public/api/v1/health
```

#### 測試用戶註冊
```bash
curl -X POST http://localhost/diary/app/api-server/public/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "display_name": "測試用戶"
  }'
```

#### 測試用戶登入
```bash
curl -X POST http://localhost/diary/app/api-server/public/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

#### 測試創建日記 (需要 token)
```bash
curl -X POST http://localhost/diary/app/api-server/public/api/v1/entries \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "content": "今天是美好的一天！",
    "emotion": "gratitude",
    "tags": ["生活", "感恩"],
    "privacy": "public"
  }'
```

## 📁 項目結構
```
api-server/
├── app/
│   ├── Controllers/
│   │   ├── AuthController.php
│   │   └── EntryController.php
│   ├── Models/
│   │   ├── BaseModel.php
│   │   ├── User.php
│   │   └── Entry.php
│   ├── Middleware/
│   │   ├── AuthMiddleware.php
│   │   └── CorsMiddleware.php
│   └── Services/
│       ├── Database.php
│       └── AuthService.php
├── config/
│   └── database.php
├── database/
│   ├── migrations/
│   │   └── 001_create_tables.sql
│   └── seeds/
│       └── initial_data.sql
├── public/
│   ├── index.php
│   └── .htaccess
├── routes/
│   └── api.php
├── storage/
│   └── uploads/
├── vendor/
├── .env
├── composer.json
└── composer.lock
```

## 🔧 配置說明

### 環境變數 (.env)
```
APP_NAME="Gratitude Diary API"
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost/diary/app/api-server/

DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=gratitude_diary
DB_USERNAME=gratitude_user
DB_PASSWORD=admin!@#!@#

JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
JWT_EXPIRE=86400
```

## 🎯 下一步建議

1. **前端整合** - 將 API 與前端應用連接
2. **功能測試** - 測試所有 API 端點
3. **性能優化** - 添加緩存和索引
4. **安全加固** - 添加速率限制和輸入驗證
5. **部署準備** - 配置生產環境

## 🐛 故障排除

### 常見問題
1. **500 錯誤** - 檢查 PHP 錯誤日誌
2. **數據庫連接失敗** - 確認數據庫配置
3. **CORS 錯誤** - 檢查前端域名配置
4. **JWT 錯誤** - 確認 JWT_SECRET 配置

### 調試模式
在 .env 中設置 `APP_DEBUG=true` 可以看到詳細錯誤信息。

## 🎊 恭喜！

您的感恩日記 API 已經完全可用！所有核心功能都已實現，包括用戶認證、日記管理、搜索和統計功能。
