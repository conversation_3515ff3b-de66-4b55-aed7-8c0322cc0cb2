import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  AppBar,
  Toolbar,
  IconButton,
  TextField,
  Button,
  Avatar,
  Card,
  CardContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  Snackbar,
  Alert,
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import PhotoCameraIcon from '@mui/icons-material/PhotoCamera';
import { motion } from 'framer-motion';

const PersonalProfilePage = () => {
  const navigate = useNavigate();
  const [profile, setProfile] = useState({
    name: '張小明',
    email: '<EMAIL>',
    bio: '熱愛生活，感恩每一天的美好時刻。',
    avatar: '👤',
  });
  const [showAvatarDialog, setShowAvatarDialog] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  const avatarOptions = ['👤', '😊', '🌟', '💖', '🙏', '🌸', '🦋', '🌈', '☀️', '🌙'];

  const handleBack = () => {
    navigate('/app/settings');
  };

  const handleSave = () => {
    // 這裡處理保存邏輯
    console.log('保存個人資料:', profile);
    setIsEditing(false);
    setShowSuccessMessage(true);
  };

  const handleCancel = () => {
    // 重置到原始狀態
    setProfile({
      name: '張小明',
      email: '<EMAIL>',
      bio: '熱愛生活，感恩每一天的美好時刻。',
      avatar: '👤',
    });
    setIsEditing(false);
  };

  const handleAvatarSelect = (selectedAvatar) => {
    setProfile(prev => ({ ...prev, avatar: selectedAvatar }));
    setShowAvatarDialog(false);
    setIsEditing(true);
  };

  const handleInputChange = (field, value) => {
    setProfile(prev => ({ ...prev, [field]: value }));
    setIsEditing(true);
  };

  return (
    <Box sx={{ 
      bgcolor: 'background.default', 
      minHeight: '100vh',
      width: '100%',
      maxWidth: '100vw',
      margin: '0 auto',
    }}>
      {/* 頂部導航欄 */}
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar sx={{ maxWidth: '600px', width: '100%', margin: '0 auto' }}>
          <IconButton onClick={handleBack} sx={{ mr: 2 }}>
            <ArrowBackIcon sx={{ color: 'text.primary' }} />
          </IconButton>
          <Typography
            variant="h3"
            sx={{
              flex: 1,
              color: 'text.primary',
              fontWeight: 600,
            }}
          >
            個人資料
          </Typography>
          {isEditing && (
            <Button
              onClick={handleSave}
              variant="contained"
              size="small"
              sx={{ ml: 1 }}
            >
              保存
            </Button>
          )}
        </Toolbar>
      </AppBar>

      <Box sx={{ 
        p: 2,
        maxWidth: '600px',
        width: '100%',
        margin: '0 auto',
      }}>
        {/* 頭像設定 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card sx={{ mb: 3, borderRadius: 4 }}>
            <CardContent sx={{ textAlign: 'center', py: 4 }}>
              <Box sx={{ position: 'relative', display: 'inline-block' }}>
                <Avatar
                  sx={{
                    width: 100,
                    height: 100,
                    mx: 'auto',
                    mb: 2,
                    bgcolor: 'secondary.main',
                    fontSize: '3rem',
                    border: '3px solid',
                    borderColor: 'secondary.light',
                    cursor: 'pointer',
                  }}
                  onClick={() => setShowAvatarDialog(true)}
                >
                  {profile.avatar}
                </Avatar>
                <IconButton
                  sx={{
                    position: 'absolute',
                    bottom: 8,
                    right: -8,
                    bgcolor: 'primary.main',
                    color: 'white',
                    width: 32,
                    height: 32,
                    '&:hover': {
                      bgcolor: 'primary.dark',
                    },
                  }}
                  onClick={() => setShowAvatarDialog(true)}
                >
                  <PhotoCameraIcon fontSize="small" />
                </IconButton>
              </Box>
              <Typography variant="body2" color="text.secondary">
                點擊頭像更換
              </Typography>
            </CardContent>
          </Card>
        </motion.div>

        {/* 基本信息 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card sx={{ mb: 3, borderRadius: 4 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                基本信息
              </Typography>
              
              <TextField
                fullWidth
                label="暱稱"
                value={profile.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                sx={{ mb: 3 }}
                variant="outlined"
              />

              <TextField
                fullWidth
                label="電子郵件"
                value={profile.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                sx={{ mb: 3 }}
                variant="outlined"
                type="email"
              />

              <TextField
                fullWidth
                label="個人簡介"
                value={profile.bio}
                onChange={(e) => handleInputChange('bio', e.target.value)}
                multiline
                rows={3}
                variant="outlined"
                placeholder="分享一些關於你的故事..."
              />
            </CardContent>
          </Card>
        </motion.div>

        {/* 操作按鈕 */}
        {isEditing && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
              <Button
                variant="outlined"
                onClick={handleCancel}
                sx={{ px: 4 }}
              >
                取消
              </Button>
              <Button
                variant="contained"
                onClick={handleSave}
                sx={{ px: 4 }}
              >
                保存變更
              </Button>
            </Box>
          </motion.div>
        )}
      </Box>

      {/* 頭像選擇對話框 */}
      <Dialog
        open={showAvatarDialog}
        onClose={() => setShowAvatarDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>選擇頭像</DialogTitle>
        <DialogContent>
          <Box sx={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(5, 1fr)', 
            gap: 2, 
            py: 2 
          }}>
            {avatarOptions.map((avatar, index) => (
              <Button
                key={index}
                onClick={() => handleAvatarSelect(avatar)}
                sx={{
                  width: 60,
                  height: 60,
                  borderRadius: '50%',
                  fontSize: '2rem',
                  border: profile.avatar === avatar ? '3px solid' : '1px solid',
                  borderColor: profile.avatar === avatar ? 'primary.main' : 'divider',
                  bgcolor: profile.avatar === avatar ? 'primary.light' : 'background.paper',
                  '&:hover': {
                    bgcolor: 'primary.light',
                  },
                }}
              >
                {avatar}
              </Button>
            ))}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAvatarDialog(false)}>
            取消
          </Button>
        </DialogActions>
      </Dialog>

      {/* 成功提示 */}
      <Snackbar
        open={showSuccessMessage}
        autoHideDuration={3000}
        onClose={() => setShowSuccessMessage(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert 
          onClose={() => setShowSuccessMessage(false)} 
          severity="success"
          sx={{ width: '100%' }}
        >
          個人資料已成功更新！
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default PersonalProfilePage;
