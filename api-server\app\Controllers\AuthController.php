<?php
require_once __DIR__ . '/../Models/User.php';
require_once __DIR__ . '/../Services/AuthService.php';

/**
 * 認證控制器
 */
class AuthController {
    private $userModel;

    public function __construct() {
        $this->userModel = new User();
        AuthService::init();
    }

    /**
     * 用戶註冊
     */
    public function register() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            // 驗證必填字段
            $required = ['email', 'password', 'display_name'];
            foreach ($required as $field) {
                if (empty($input[$field])) {
                    $this->error("Missing required field: $field", 400);
                    return;
                }
            }
            
            // 驗證email格式
            if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
                $this->error('Invalid email format', 400);
                return;
            }
            
            // 檢查email是否已存在
            if ($this->userModel->emailExists($input['email'])) {
                $this->error('Email already exists', 409);
                return;
            }
            
            // 驗證密碼強度
            if (strlen($input['password']) < 6) {
                $this->error('Password must be at least 6 characters long', 400);
                return;
            }
            
            // 創建用戶
            $userData = [
                'email' => $input['email'],
                'password' => $input['password'],
                'display_name' => $input['display_name'],
                'bio' => $input['bio'] ?? '',
                'language' => $input['language'] ?? 'zh-TW',
                'timezone' => $input['timezone'] ?? 'Asia/Taipei'
            ];
            
            $user = $this->userModel->createUser($userData);
            
            if (!$user) {
                $this->error('Failed to create user', 500);
                return;
            }
            
            // 生成JWT token
            $token = AuthService::generateToken($user['id'], $user['email']);
            
            // 隱藏敏感信息
            $userProfile = $this->userModel->getProfile($user['id']);
            
            $this->success([
                'message' => 'User registered successfully',
                'user' => $userProfile,
                'token' => $token
            ], 201);
            
        } catch (Exception $e) {
            $this->error('Registration failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 用戶登入
     */
    public function login() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            // 驗證必填字段
            if (empty($input['email']) || empty($input['password'])) {
                $this->error('Email and password are required', 400);
                return;
            }
            
            // 查找用戶
            $user = $this->userModel->findByEmail($input['email']);
            
            if (!$user) {
                $this->error('Invalid credentials', 401);
                return;
            }
            
            // 驗證密碼
            if (!AuthService::verifyPassword($input['password'], $user['password'])) {
                $this->error('Invalid credentials', 401);
                return;
            }
            
            // 生成JWT token
            $token = AuthService::generateToken($user['id'], $user['email']);
            
            // 更新最後活動時間
            $this->userModel->updateLastActivity($user['id']);
            
            // 隱藏敏感信息
            $userProfile = $this->userModel->getProfile($user['id']);
            
            $this->success([
                'message' => 'Login successful',
                'user' => $userProfile,
                'token' => $token
            ]);
            
        } catch (Exception $e) {
            $this->error('Login failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 獲取當前用戶信息
     */
    public function me() {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                $this->error('User not authenticated', 401);
                return;
            }
            
            $userProfile = $this->userModel->getProfile($userId);
            $userStats = $this->userModel->getUserStats($userId);
            $achievements = $this->userModel->getUserAchievements($userId);
            
            $this->success([
                'user' => $userProfile,
                'stats' => $userStats,
                'achievements' => $achievements
            ]);
            
        } catch (Exception $e) {
            $this->error('Failed to get user info: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新用戶資料
     */
    public function updateProfile() {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            $input = json_decode(file_get_contents('php://input'), true);
            
            // 允許更新的字段
            $allowedFields = [
                'display_name', 'bio', 'avatar', 'language', 'timezone', 'theme',
                'profile_visible', 'allow_comments', 'allow_messages',
                'daily_reminder', 'focus_reminder', 'social_interaction', 'push_enabled'
            ];
            
            $updateData = [];
            foreach ($allowedFields as $field) {
                if (isset($input[$field])) {
                    $updateData[$field] = $input[$field];
                }
            }
            
            if (empty($updateData)) {
                $this->error('No valid fields to update', 400);
                return;
            }
            
            // 如果更新email，檢查是否已存在
            if (isset($input['email'])) {
                if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
                    $this->error('Invalid email format', 400);
                    return;
                }
                
                if ($this->userModel->emailExists($input['email'], $userId)) {
                    $this->error('Email already exists', 409);
                    return;
                }
                
                $updateData['email'] = $input['email'];
            }
            
            $updatedUser = $this->userModel->update($userId, $updateData);
            $userProfile = $this->userModel->getProfile($userId);
            
            $this->success([
                'message' => 'Profile updated successfully',
                'user' => $userProfile
            ]);
            
        } catch (Exception $e) {
            $this->error('Failed to update profile: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 更改密碼
     */
    public function changePassword() {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (empty($input['current_password']) || empty($input['new_password'])) {
                $this->error('Current password and new password are required', 400);
                return;
            }
            
            // 驗證當前密碼
            if (!$this->userModel->verifyPassword($userId, $input['current_password'])) {
                $this->error('Current password is incorrect', 400);
                return;
            }
            
            // 驗證新密碼強度
            if (strlen($input['new_password']) < 6) {
                $this->error('New password must be at least 6 characters long', 400);
                return;
            }
            
            // 更新密碼
            $this->userModel->updatePassword($userId, $input['new_password']);
            
            $this->success(['message' => 'Password changed successfully']);
            
        } catch (Exception $e) {
            $this->error('Failed to change password: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 登出（客戶端處理token失效）
     */
    public function logout() {
        $this->success(['message' => 'Logout successful']);
    }

    /**
     * 成功響應
     */
    private function success($data, $code = 200) {
        http_response_code($code);
        header('Content-Type: application/json');
        echo json_encode(array_merge(['success' => true], $data));
    }

    /**
     * 錯誤響應
     */
    private function error($message, $code = 400) {
        http_response_code($code);
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'error' => $message
        ]);
    }
}
