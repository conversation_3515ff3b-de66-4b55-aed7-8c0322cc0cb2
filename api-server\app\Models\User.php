<?php
require_once __DIR__ . '/BaseModel.php';

/**
 * 用戶模型
 */
class User extends BaseModel {
    protected $table = 'users';
    protected $fillable = [
        'uid', 'email', 'password', 'display_name', 'avatar', 'bio',
        'language', 'timezone', 'theme', 'profile_visible', 'allow_comments',
        'allow_messages', 'daily_reminder', 'focus_reminder', 'social_interaction',
        'push_enabled'
    ];
    protected $hidden = ['password'];

    /**
     * 根據email查找用戶
     */
    public function findByEmail($email) {
        return $this->whereFirst('email = :email', ['email' => $email]);
    }

    /**
     * 根據UID查找用戶
     */
    public function findByUid($uid) {
        return $this->whereFirst('uid = :uid', ['uid' => $uid]);
    }

    /**
     * 創建用戶
     */
    public function createUser($data) {
        // 加密密碼
        if (isset($data['password'])) {
            $data['password'] = AuthService::hashPassword($data['password']);
        }

        // 生成唯一UID
        if (!isset($data['uid'])) {
            $data['uid'] = $this->generateUniqueUid();
        }

        return $this->create($data);
    }

    /**
     * 更新用戶密碼
     */
    public function updatePassword($userId, $newPassword) {
        $hashedPassword = AuthService::hashPassword($newPassword);
        return $this->update($userId, ['password' => $hashedPassword]);
    }

    /**
     * 驗證用戶密碼
     */
    public function verifyPassword($userId, $password) {
        $user = $this->find($userId);
        if (!$user) {
            return false;
        }
        return AuthService::verifyPassword($password, $user['password']);
    }

    /**
     * 更新用戶統計
     */
    public function updateStats($userId, $stats) {
        $allowedStats = [
            'total_entries', 'total_likes', 'total_comments',
            'focus_sessions', 'focus_minutes', 'streak_days',
            'last_entry_date'
        ];
        
        $filteredStats = array_intersect_key($stats, array_flip($allowedStats));
        return $this->update($userId, $filteredStats);
    }

    /**
     * 獲取用戶統計
     */
    public function getUserStats($userId) {
        $sql = "SELECT 
                    total_entries, total_likes, total_comments,
                    focus_sessions, focus_minutes, streak_days,
                    last_entry_date, created_at
                FROM {$this->table} 
                WHERE id = :id";
        return $this->db->fetchOne($sql, ['id' => $userId]);
    }

    /**
     * 獲取用戶成就
     */
    public function getUserAchievements($userId) {
        $sql = "SELECT a.*, ua.unlocked_at 
                FROM achievements a
                JOIN user_achievements ua ON a.id = ua.achievement_id
                WHERE ua.user_id = :user_id
                ORDER BY ua.unlocked_at DESC";
        return $this->db->fetchAll($sql, ['user_id' => $userId]);
    }

    /**
     * 檢查用戶是否已獲得成就
     */
    public function hasAchievement($userId, $achievementId) {
        $sql = "SELECT 1 FROM user_achievements 
                WHERE user_id = :user_id AND achievement_id = :achievement_id";
        return (bool) $this->db->fetchColumn($sql, [
            'user_id' => $userId,
            'achievement_id' => $achievementId
        ]);
    }

    /**
     * 授予用戶成就
     */
    public function grantAchievement($userId, $achievementId) {
        if ($this->hasAchievement($userId, $achievementId)) {
            return false; // 已經擁有該成就
        }

        $data = [
            'user_id' => $userId,
            'achievement_id' => $achievementId,
            'unlocked_at' => date('Y-m-d H:i:s')
        ];

        return $this->db->insert('user_achievements', $data);
    }

    /**
     * 獲取活躍用戶
     */
    public function getActiveUsers($days = 7, $limit = 10) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE last_entry_date >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
                ORDER BY total_entries DESC, last_entry_date DESC
                LIMIT :limit";
        return $this->db->fetchAll($sql, ['days' => $days, 'limit' => $limit]);
    }

    /**
     * 搜索用戶
     */
    public function searchUsers($searchTerm, $page = 1, $limit = 20) {
        return $this->search(['display_name', 'email', 'bio'], $searchTerm, $page, $limit);
    }

    /**
     * 生成唯一UID
     */
    private function generateUniqueUid() {
        do {
            $uid = 'user_' . uniqid() . '_' . random_int(1000, 9999);
        } while ($this->findByUid($uid));
        
        return $uid;
    }

    /**
     * 獲取用戶資料（隱藏敏感信息）
     */
    public function getProfile($userId) {
        $user = $this->find($userId);
        if ($user) {
            return $this->hideFields($user);
        }
        return null;
    }

    /**
     * 更新最後活動時間
     */
    public function updateLastActivity($userId) {
        return $this->update($userId, ['updated_at' => date('Y-m-d H:i:s')]);
    }

    /**
     * 檢查email是否已存在
     */
    public function emailExists($email, $excludeUserId = null) {
        $sql = "SELECT 1 FROM {$this->table} WHERE email = :email";
        $params = ['email' => $email];
        
        if ($excludeUserId) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeUserId;
        }
        
        return (bool) $this->db->fetchColumn($sql, $params);
    }
}
