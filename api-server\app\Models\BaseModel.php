<?php
/**
 * 基礎模型類
 * 提供通用的數據庫操作方法
 */
class BaseModel {
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    protected $hidden = [];
    protected $timestamps = true;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * 查找所有記錄
     */
    public function all($orderBy = 'created_at DESC', $limit = null) {
        $sql = "SELECT * FROM {$this->table} ORDER BY {$orderBy}";
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        return $this->db->fetchAll($sql);
    }

    /**
     * 根據ID查找記錄
     */
    public function find($id) {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = :id";
        return $this->db->fetchOne($sql, ['id' => $id]);
    }

    /**
     * 根據條件查找記錄
     */
    public function where($conditions, $params = []) {
        $sql = "SELECT * FROM {$this->table} WHERE {$conditions}";
        return $this->db->fetchAll($sql, $params);
    }

    /**
     * 根據條件查找單條記錄
     */
    public function whereFirst($conditions, $params = []) {
        $sql = "SELECT * FROM {$this->table} WHERE {$conditions} LIMIT 1";
        return $this->db->fetchOne($sql, $params);
    }

    /**
     * 創建新記錄
     */
    public function create($data) {
        // 過濾可填充字段
        $filteredData = $this->filterFillable($data);
        
        // 添加時間戳
        if ($this->timestamps) {
            $filteredData['created_at'] = date('Y-m-d H:i:s');
            $filteredData['updated_at'] = date('Y-m-d H:i:s');
        }

        $id = $this->db->insert($this->table, $filteredData);
        return $this->find($id);
    }

    /**
     * 更新記錄
     */
    public function update($id, $data) {
        // 過濾可填充字段
        $filteredData = $this->filterFillable($data);
        
        // 添加更新時間戳
        if ($this->timestamps) {
            $filteredData['updated_at'] = date('Y-m-d H:i:s');
        }

        $where = "{$this->primaryKey} = :id";
        $whereParams = ['id' => $id];
        
        $this->db->update($this->table, $filteredData, $where, $whereParams);
        return $this->find($id);
    }

    /**
     * 刪除記錄
     */
    public function delete($id) {
        $sql = "DELETE FROM {$this->table} WHERE {$this->primaryKey} = :id";
        return $this->db->query($sql, ['id' => $id]);
    }

    /**
     * 分頁查詢
     */
    public function paginate($page = 1, $limit = 20, $orderBy = 'created_at DESC', $where = '', $params = []) {
        return $this->db->paginate(
            "SELECT * FROM {$this->table}" . ($where ? " WHERE {$where}" : '') . " ORDER BY {$orderBy}",
            $params,
            $page,
            $limit
        );
    }

    /**
     * 計數
     */
    public function count($where = '', $params = []) {
        $sql = "SELECT COUNT(*) FROM {$this->table}" . ($where ? " WHERE {$where}" : '');
        return $this->db->fetchColumn($sql, $params);
    }

    /**
     * 過濾可填充字段
     */
    protected function filterFillable($data) {
        if (empty($this->fillable)) {
            return $data;
        }

        return array_intersect_key($data, array_flip($this->fillable));
    }

    /**
     * 隱藏敏感字段
     */
    protected function hideFields($data) {
        if (empty($this->hidden)) {
            return $data;
        }

        if (is_array($data) && isset($data[0])) {
            // 多條記錄
            return array_map(function($item) {
                return array_diff_key($item, array_flip($this->hidden));
            }, $data);
        } else {
            // 單條記錄
            return array_diff_key($data, array_flip($this->hidden));
        }
    }

    /**
     * 檢查記錄是否存在
     */
    public function exists($id) {
        $sql = "SELECT 1 FROM {$this->table} WHERE {$this->primaryKey} = :id";
        return (bool) $this->db->fetchColumn($sql, ['id' => $id]);
    }

    /**
     * 獲取最新記錄
     */
    public function latest($limit = 10) {
        return $this->all('created_at DESC', $limit);
    }

    /**
     * 搜索
     */
    public function search($searchFields, $searchTerm, $page = 1, $limit = 20) {
        return $this->db->search($this->table, $searchFields, $searchTerm, '', [], $page, $limit);
    }
}
