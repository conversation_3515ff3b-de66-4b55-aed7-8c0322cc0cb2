<?php
// 測試路由系統
require_once '../vendor/autoload.php';

use Dotenv\Dotenv;

// 載入環境變數
$dotenv = Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// 設置模擬請求環境
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/api/v1/health';
$_SERVER['HTTP_HOST'] = 'localhost';

echo "Testing route system...\n\n";

try {
    // 載入所有必要的文件
    require_once '../app/Services/Database.php';
    require_once '../app/Services/AuthService.php';
    require_once '../app/Middleware/CorsMiddleware.php';
    require_once '../app/Middleware/AuthMiddleware.php';
    
    echo "✅ All services loaded\n";
    
    // 載入路由
    $router = require_once '../routes/api.php';
    echo "✅ Router loaded\n";
    
    // 測試健康檢查路由
    ob_start();
    $router->handleRequest();
    $output = ob_get_clean();
    
    echo "✅ Route handled successfully\n";
    echo "Response: " . $output . "\n";
    
} catch (Exception $e) {
    echo "❌ Route error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
