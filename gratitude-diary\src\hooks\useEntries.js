/**
 * 日記相關的 React Hook
 */

import { useState, useEffect, useCallback } from 'react';
import { entryService } from '../services/entryService.js';

// 獲取日記列表的 Hook
export const useEntries = (params = {}) => {
  const [entries, setEntries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchEntries = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await entryService.getEntries(params);
      
      if (result.success) {
        setEntries(result.entries);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message || '獲取日記列表失敗');
    } finally {
      setLoading(false);
    }
  }, [params]);

  useEffect(() => {
    fetchEntries();
  }, [fetchEntries]);

  return {
    entries,
    loading,
    error,
    refetch: fetchEntries,
    clearError: () => setError(null)
  };
};

// 獲取單個日記的 Hook
export const useEntry = (entryId) => {
  const [entry, setEntry] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchEntry = useCallback(async () => {
    if (!entryId) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await entryService.getEntry(entryId);
      
      if (result.success) {
        setEntry(result.entry);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message || '獲取日記失敗');
    } finally {
      setLoading(false);
    }
  }, [entryId]);

  useEffect(() => {
    fetchEntry();
  }, [fetchEntry]);

  return {
    entry,
    loading,
    error,
    refetch: fetchEntry,
    clearError: () => setError(null)
  };
};

// 創建日記的 Hook
export const useCreateEntry = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const createEntry = useCallback(async (entryData) => {
    setLoading(true);
    setError(null);

    try {
      const result = await entryService.createEntry(entryData);
      
      if (!result.success) {
        setError(result.error);
      }
      
      return result;
    } catch (err) {
      const errorMessage = err.message || '創建日記失敗';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    createEntry,
    loading,
    error,
    clearError: () => setError(null)
  };
};

// 更新日記的 Hook
export const useUpdateEntry = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const updateEntry = useCallback(async (entryId, entryData) => {
    setLoading(true);
    setError(null);

    try {
      const result = await entryService.updateEntry(entryId, entryData);
      
      if (!result.success) {
        setError(result.error);
      }
      
      return result;
    } catch (err) {
      const errorMessage = err.message || '更新日記失敗';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    updateEntry,
    loading,
    error,
    clearError: () => setError(null)
  };
};

// 刪除日記的 Hook
export const useDeleteEntry = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const deleteEntry = useCallback(async (entryId) => {
    setLoading(true);
    setError(null);

    try {
      const result = await entryService.deleteEntry(entryId);
      
      if (!result.success) {
        setError(result.error);
      }
      
      return result;
    } catch (err) {
      const errorMessage = err.message || '刪除日記失敗';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    deleteEntry,
    loading,
    error,
    clearError: () => setError(null)
  };
};

// 搜索日記的 Hook
export const useSearchEntries = () => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const searchEntries = useCallback(async (searchTerm, params = {}) => {
    if (!searchTerm.trim()) {
      setResults([]);
      return { success: true, results: [] };
    }

    setLoading(true);
    setError(null);

    try {
      const result = await entryService.searchEntries(searchTerm, params);
      
      if (result.success) {
        setResults(result.results);
      } else {
        setError(result.error);
      }
      
      return result;
    } catch (err) {
      const errorMessage = err.message || '搜索失敗';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  const clearResults = useCallback(() => {
    setResults([]);
    setError(null);
  }, []);

  return {
    results,
    loading,
    error,
    searchEntries,
    clearResults,
    clearError: () => setError(null)
  };
};

// 獲取統計數據的 Hook
export const useStats = (params = {}) => {
  const [stats, setStats] = useState(null);
  const [emotions, setEmotions] = useState([]);
  const [streak, setStreak] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchStats = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await entryService.getStats(params);
      
      if (result.success) {
        setStats(result.stats);
        setEmotions(result.emotions || []);
        setStreak(result.streak || 0);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message || '獲取統計數據失敗');
    } finally {
      setLoading(false);
    }
  }, [params]);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return {
    stats,
    emotions,
    streak,
    loading,
    error,
    refetch: fetchStats,
    clearError: () => setError(null)
  };
};

export default useEntries;
