# PHP API 建置指南

## 環境要求

### 1. 服務器環境
- **PHP**: 8.1+ (推薦8.2)
- **MySQL**: 8.0+ 或 MariaDB 10.6+
- **Web服務器**: Apache 2.4+ 或 Nginx 1.18+
- **SSL證書**: 必須支援HTTPS
- **擴展**: PDO, JSON, OpenSSL, cURL, GD, mbstring

### 2. 開發環境設置
```bash
# 使用XAMPP/WAMP (Windows)
# 或 LAMP (Linux)
# 或 MAMP (macOS)

# 檢查PHP版本和擴展
php -v
php -m | grep -E "(pdo|json|openssl|curl|gd|mbstring)"
```

## 專案初始化

### 1. 目錄結構創建
```bash
mkdir gratitude-diary-api
cd gratitude-diary-api

# 創建目錄結構
mkdir -p {app/{Controllers,Models,Services,Middleware},config,routes,public,storage/{logs,uploads},database}

# 創建基本文件
touch public/index.php
touch routes/api.php
touch config/{database,firebase,app}.php
touch .env
touch .htaccess
```

### 2. Composer初始化
```bash
# 初始化composer
composer init

# 安裝必要依賴
composer require firebase/php-jwt
composer require vlucas/phpdotenv
composer require monolog/monolog
composer require intervention/image
composer require phpmailer/phpmailer

# 開發依賴
composer require --dev phpunit/phpunit
composer require --dev squizlabs/php_codesniffer
```

### 3. 環境配置文件
```bash
# .env
APP_NAME="Gratitude Diary API"
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost/gratitude-diary-api

# 數據庫配置
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=gratitude_diary
DB_USERNAME=root
DB_PASSWORD=

# Firebase配置
FIREBASE_PROJECT_ID=your-project-id
FCM_SERVER_KEY=your-fcm-server-key
FCM_SENDER_ID=your-sender-id
FIREBASE_API_KEY=your-api-key

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRE=86400

# 文件上傳配置
UPLOAD_PATH=storage/uploads
MAX_FILE_SIZE=10485760
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,mp3,wav,m4a

# 郵件配置
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Gratitude Diary"
```

## 核心文件實作

### 1. 應用程式入口點
```php
<?php
// public/index.php
require_once '../vendor/autoload.php';

use Dotenv\Dotenv;

// 載入環境變數
$dotenv = Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// 設置錯誤報告
if ($_ENV['APP_DEBUG'] === 'true') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// 設置時區
date_default_timezone_set('Asia/Taipei');

// CORS處理
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 路由處理
require_once '../routes/api.php';
```

### 2. 數據庫連接類
```php
<?php
// app/Services/Database.php
class Database {
    private static $instance = null;
    private $connection;

    private function __construct() {
        $config = require __DIR__ . '/../../config/database.php';
        
        try {
            $dsn = "mysql:host={$config['host']};dbname={$config['database']};charset={$config['charset']}";
            $this->connection = new PDO($dsn, $config['username'], $config['password'], $config['options']);
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("Database connection failed");
        }
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getConnection() {
        return $this->connection;
    }

    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Query failed: " . $e->getMessage());
            throw new Exception("Query execution failed");
        }
    }

    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->query($sql, $data);
        
        return $this->connection->lastInsertId();
    }

    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $key) {
            $setClause[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setClause);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $whereParams);
        
        return $this->query($sql, $params);
    }

    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        return $this->query($sql, $params);
    }
}
```

### 3. JWT認證服務
```php
<?php
// app/Services/AuthService.php
require_once '../vendor/autoload.php';
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

class AuthService {
    private static $secretKey;
    private static $algorithm = 'HS256';
    private static $expireTime = 86400; // 24小時

    public static function init() {
        self::$secretKey = $_ENV['JWT_SECRET'];
        self::$expireTime = $_ENV['JWT_EXPIRE'] ?? 86400;
    }

    public static function generateToken($userId, $email) {
        $payload = [
            'iss' => $_ENV['APP_URL'],
            'aud' => $_ENV['APP_URL'],
            'iat' => time(),
            'exp' => time() + self::$expireTime,
            'user_id' => $userId,
            'email' => $email
        ];

        return JWT::encode($payload, self::$secretKey, self::$algorithm);
    }

    public static function validateToken($token) {
        try {
            $decoded = JWT::decode($token, new Key(self::$secretKey, self::$algorithm));
            return (array) $decoded;
        } catch (Exception $e) {
            return false;
        }
    }

    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
    }

    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }

    public static function getCurrentUser() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? '';
        
        if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return false;
        }

        $token = $matches[1];
        return self::validateToken($token);
    }
}

// 初始化
AuthService::init();
```

### 4. FCM推播服務
```php
<?php
// app/Services/FCMService.php
class FCMService {
    private $serverKey;
    private $senderId;
    private $apiUrl = 'https://fcm.googleapis.com/fcm/send';

    public function __construct() {
        $this->serverKey = $_ENV['FCM_SERVER_KEY'];
        $this->senderId = $_ENV['FCM_SENDER_ID'];
    }

    public function sendToUser($userId, $title, $body, $data = []) {
        // 獲取用戶的FCM tokens
        $db = Database::getInstance();
        $tokens = $db->fetchAll(
            "SELECT token FROM fcm_tokens WHERE user_id = ? AND is_active = 1",
            [$userId]
        );

        if (empty($tokens)) {
            return ['success' => false, 'message' => '用戶沒有有效的FCM token'];
        }

        $tokenList = array_column($tokens, 'token');
        return $this->sendToTokens($tokenList, $title, $body, $data);
    }

    public function sendToTokens($tokens, $title, $body, $data = []) {
        $notification = [
            'title' => $title,
            'body' => $body,
            'sound' => 'default',
            'badge' => 1
        ];

        $payload = [
            'registration_ids' => $tokens,
            'notification' => $notification,
            'data' => $data,
            'priority' => 'high'
        ];

        $headers = [
            'Authorization: key=' . $this->serverKey,
            'Content-Type: application/json'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode === 200) {
            $result = json_decode($response, true);
            return [
                'success' => true,
                'response' => $result,
                'sent_count' => $result['success'] ?? 0,
                'failed_count' => $result['failure'] ?? 0
            ];
        } else {
            return [
                'success' => false,
                'error' => 'FCM request failed',
                'http_code' => $httpCode,
                'response' => $response
            ];
        }
    }

    public function saveToken($userId, $token, $platform) {
        $db = Database::getInstance();
        
        // 檢查token是否已存在
        $existing = $db->fetchOne(
            "SELECT id FROM fcm_tokens WHERE user_id = ? AND token = ?",
            [$userId, $token]
        );

        if ($existing) {
            // 更新現有token
            $db->update(
                'fcm_tokens',
                ['is_active' => 1, 'updated_at' => date('Y-m-d H:i:s')],
                'id = ?',
                [$existing['id']]
            );
        } else {
            // 插入新token
            $db->insert('fcm_tokens', [
                'user_id' => $userId,
                'token' => $token,
                'platform' => $platform,
                'is_active' => 1
            ]);
        }

        return true;
    }

    public function removeToken($userId, $token) {
        $db = Database::getInstance();
        return $db->update(
            'fcm_tokens',
            ['is_active' => 0],
            'user_id = ? AND token = ?',
            [$userId, $token]
        );
    }
}
```

### 5. 用戶模型
```php
<?php
// app/Models/User.php
class User {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    public function create($data) {
        // 檢查email是否已存在
        if ($this->findByEmail($data['email'])) {
            throw new Exception('Email already exists');
        }

        // 加密密碼
        $data['password'] = AuthService::hashPassword($data['password']);
        
        // 生成唯一UID
        $data['uid'] = $this->generateUID();

        // 插入用戶數據
        $userId = $this->db->insert('users', [
            'uid' => $data['uid'],
            'email' => $data['email'],
            'display_name' => $data['display_name'],
            'password' => $data['password']
        ]);

        // 保存FCM token
        if (!empty($data['fcm_token'])) {
            $fcmService = new FCMService();
            $fcmService->saveToken($userId, $data['fcm_token'], $data['platform'] ?? 'web');
        }

        return $this->findById($userId);
    }

    public function findById($id) {
        return $this->db->fetchOne(
            "SELECT id, uid, email, display_name, avatar, bio, language, timezone, theme,
                    profile_visible, allow_comments, allow_messages,
                    daily_reminder, focus_reminder, social_interaction, push_enabled,
                    total_entries, total_likes, total_comments, focus_sessions, focus_minutes, streak_days,
                    created_at, updated_at
             FROM users WHERE id = ?",
            [$id]
        );
    }

    public function findByEmail($email) {
        return $this->db->fetchOne(
            "SELECT id, uid, email, display_name, password, created_at FROM users WHERE email = ?",
            [$email]
        );
    }

    public function findByUID($uid) {
        return $this->db->fetchOne(
            "SELECT id, uid, email, display_name, created_at FROM users WHERE uid = ?",
            [$uid]
        );
    }

    public function update($id, $data) {
        // 移除不允許更新的字段
        unset($data['id'], $data['uid'], $data['email'], $data['password'], $data['created_at']);
        
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $this->db->update('users', $data, 'id = ?', [$id]);
        return $this->findById($id);
    }

    public function updateStats($userId, $stats) {
        $this->db->update('users', $stats, 'id = ?', [$userId]);
    }

    public function incrementStat($userId, $field, $amount = 1) {
        $this->db->query(
            "UPDATE users SET {$field} = {$field} + ?, updated_at = NOW() WHERE id = ?",
            [$amount, $userId]
        );
    }

    private function generateUID() {
        return 'user_' . uniqid() . '_' . time();
    }

    public function authenticate($email, $password) {
        $user = $this->findByEmail($email);
        
        if (!$user || !AuthService::verifyPassword($password, $user['password'])) {
            return false;
        }

        // 移除密碼字段
        unset($user['password']);
        return $user;
    }
}
```

### 6. 認證控制器
```php
<?php
// app/Controllers/AuthController.php
class AuthController {
    private $userModel;

    public function __construct() {
        $this->userModel = new User();
    }

    public function register() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            // 驗證必要字段
            $required = ['email', 'password', 'display_name'];
            foreach ($required as $field) {
                if (empty($input[$field])) {
                    throw new Exception("Missing required field: {$field}");
                }
            }

            // 驗證email格式
            if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Invalid email format');
            }

            // 驗證密碼強度
            if (strlen($input['password']) < 6) {
                throw new Exception('Password must be at least 6 characters');
            }

            // 創建用戶
            $user = $this->userModel->create($input);
            
            // 生成JWT token
            $token = AuthService::generateToken($user['id'], $user['email']);

            $this->jsonResponse([
                'success' => true,
                'data' => [
                    'user' => $user,
                    'token' => $token
                ],
                'message' => '註冊成功'
            ], 201);

        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'error' => [
                    'code' => 'REGISTRATION_FAILED',
                    'message' => $e->getMessage()
                ]
            ], 400);
        }
    }

    public function login() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (empty($input['email']) || empty($input['password'])) {
                throw new Exception('Email and password are required');
            }

            // 驗證用戶
            $user = $this->userModel->authenticate($input['email'], $input['password']);
            
            if (!$user) {
                throw new Exception('Invalid credentials');
            }

            // 保存FCM token
            if (!empty($input['fcm_token'])) {
                $fcmService = new FCMService();
                $fcmService->saveToken($user['id'], $input['fcm_token'], $input['platform'] ?? 'web');
            }

            // 生成JWT token
            $token = AuthService::generateToken($user['id'], $user['email']);

            $this->jsonResponse([
                'success' => true,
                'data' => [
                    'user' => $user,
                    'token' => $token
                ],
                'message' => '登入成功'
            ]);

        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'error' => [
                    'code' => 'LOGIN_FAILED',
                    'message' => $e->getMessage()
                ]
            ], 401);
        }
    }

    public function logout() {
        try {
            $user = AuthService::getCurrentUser();
            if (!$user) {
                throw new Exception('Unauthorized');
            }

            $input = json_decode(file_get_contents('php://input'), true);
            
            // 移除FCM token
            if (!empty($input['fcm_token'])) {
                $fcmService = new FCMService();
                $fcmService->removeToken($user['user_id'], $input['fcm_token']);
            }

            $this->jsonResponse([
                'success' => true,
                'message' => '登出成功'
            ]);

        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'error' => [
                    'code' => 'LOGOUT_FAILED',
                    'message' => $e->getMessage()
                ]
            ], 400);
        }
    }

    private function jsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}
```

## 路由配置

### 7. API路由定義
```php
<?php
// routes/api.php
require_once '../app/Services/Database.php';
require_once '../app/Services/AuthService.php';
require_once '../app/Services/FCMService.php';
require_once '../app/Models/User.php';
require_once '../app/Controllers/AuthController.php';

// 簡單路由處理器
class Router {
    private $routes = [];

    public function addRoute($method, $path, $handler) {
        $this->routes[] = [
            'method' => $method,
            'path' => $path,
            'handler' => $handler
        ];
    }

    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $path = str_replace('/gratitude-diary-api/public', '', $path);

        foreach ($this->routes as $route) {
            if ($route['method'] === $method && $this->matchPath($route['path'], $path)) {
                $params = $this->extractParams($route['path'], $path);
                call_user_func_array($route['handler'], $params);
                return;
            }
        }

        // 404 Not Found
        http_response_code(404);
        echo json_encode(['error' => 'Route not found']);
    }

    private function matchPath($routePath, $requestPath) {
        $routePattern = preg_replace('/\{[^}]+\}/', '([^/]+)', $routePath);
        return preg_match('#^' . $routePattern . '$#', $requestPath);
    }

    private function extractParams($routePath, $requestPath) {
        $routePattern = preg_replace('/\{[^}]+\}/', '([^/]+)', $routePath);
        preg_match('#^' . $routePattern . '$#', $requestPath, $matches);
        return array_slice($matches, 1);
    }
}

// 創建路由器
$router = new Router();

// 認證路由
$authController = new AuthController();
$router->addRoute('POST', '/api/v1/auth/register', [$authController, 'register']);
$router->addRoute('POST', '/api/v1/auth/login', [$authController, 'login']);
$router->addRoute('POST', '/api/v1/auth/logout', [$authController, 'logout']);

// 處理請求
$router->handleRequest();
```

## 數據庫初始化

### 8. 數據庫遷移腳本
```sql
-- database/migrations/001_create_tables.sql
-- 創建數據庫
CREATE DATABASE IF NOT EXISTS gratitude_diary CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE gratitude_diary;

-- 用戶表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    uid VARCHAR(255) UNIQUE NOT NULL COMMENT 'Firebase UID或自定義ID',
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    avatar VARCHAR(500) NULL,
    bio TEXT NULL,
    language VARCHAR(10) DEFAULT 'zh-TW',
    timezone VARCHAR(50) DEFAULT 'Asia/Taipei',
    theme VARCHAR(20) DEFAULT 'light',

    -- 隱私設定
    profile_visible BOOLEAN DEFAULT TRUE,
    allow_comments BOOLEAN DEFAULT TRUE,
    allow_messages BOOLEAN DEFAULT FALSE,

    -- 通知設定
    daily_reminder BOOLEAN DEFAULT TRUE,
    focus_reminder BOOLEAN DEFAULT TRUE,
    social_interaction BOOLEAN DEFAULT TRUE,
    push_enabled BOOLEAN DEFAULT TRUE,

    -- 統計數據
    total_entries INT DEFAULT 0,
    total_likes INT DEFAULT 0,
    total_comments INT DEFAULT 0,
    focus_sessions INT DEFAULT 0,
    focus_minutes INT DEFAULT 0,
    streak_days INT DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_email (email),
    INDEX idx_uid (uid),
    INDEX idx_created_at (created_at)
);

-- 日記條目表
CREATE TABLE entries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content TEXT NOT NULL,
    emotion VARCHAR(50) NOT NULL,
    tags JSON NULL COMMENT '標籤數組',
    media JSON NULL COMMENT '媒體文件信息',
    privacy ENUM('public', 'friends', 'private') DEFAULT 'public',
    is_anonymous BOOLEAN DEFAULT FALSE,

    -- 位置信息
    latitude DECIMAL(10, 8) NULL,
    longitude DECIMAL(11, 8) NULL,
    address VARCHAR(500) NULL,

    -- 互動統計
    likes_count INT DEFAULT 0,
    comments_count INT DEFAULT 0,
    shares_count INT DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_emotion (emotion),
    INDEX idx_privacy (privacy),
    INDEX idx_created_at (created_at),
    INDEX idx_user_created (user_id, created_at),
    FULLTEXT idx_content (content)
);

-- FCM Token表
CREATE TABLE fcm_tokens (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    token VARCHAR(500) NOT NULL,
    platform ENUM('web', 'ios', 'android', 'desktop') NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_token (user_id, token),
    INDEX idx_user_active (user_id, is_active),
    INDEX idx_platform (platform)
);

-- 插入測試數據
INSERT INTO users (uid, email, password, display_name) VALUES
('test_user_001', '<EMAIL>', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukj/LkLjK', '測試用戶');
```

## 部署配置

### 9. Apache .htaccess配置
```apache
# public/.htaccess
RewriteEngine On

# 處理CORS預檢請求
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# 將所有請求重定向到index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# 安全設置
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

# 設置文件上傳限制
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
```

### 10. Nginx配置
```nginx
# nginx.conf
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/gratitude-diary-api/public;
    index index.php;

    # 處理CORS
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
    add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With";

    # 處理OPTIONS請求
    if ($request_method = 'OPTIONS') {
        return 200;
    }

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # 安全設置
    location ~ /\.env {
        deny all;
    }

    location ~ /\.(ht|git) {
        deny all;
    }
}
```

## 測試和調試

### 11. API測試腳本
```php
<?php
// tests/api_test.php
function testAPI($endpoint, $method = 'GET', $data = null, $token = null) {
    $url = 'http://localhost/gratitude-diary-api/public' . $endpoint;

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);

    $headers = ['Content-Type: application/json'];
    if ($token) {
        $headers[] = 'Authorization: Bearer ' . $token;
    }
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "Testing {$method} {$endpoint}\n";
    echo "HTTP Code: {$httpCode}\n";
    echo "Response: {$response}\n\n";

    return json_decode($response, true);
}

// 測試用戶註冊
$registerData = [
    'email' => 'test' . time() . '@example.com',
    'password' => 'password123',
    'display_name' => '測試用戶',
    'fcm_token' => 'test_fcm_token_' . time(),
    'platform' => 'web'
];

$registerResponse = testAPI('/api/v1/auth/register', 'POST', $registerData);

// 測試用戶登入
if ($registerResponse['success']) {
    $loginData = [
        'email' => $registerData['email'],
        'password' => $registerData['password'],
        'fcm_token' => 'test_fcm_token_login_' . time(),
        'platform' => 'web'
    ];

    $loginResponse = testAPI('/api/v1/auth/login', 'POST', $loginData);

    if ($loginResponse['success']) {
        $token = $loginResponse['data']['token'];
        echo "Login successful! Token: {$token}\n\n";

        // 測試登出
        testAPI('/api/v1/auth/logout', 'POST', ['fcm_token' => $loginData['fcm_token']], $token);
    }
}
```

這個PHP API建置指南提供了完整的後端架構，包括數據庫連接、JWT認證、FCM推播、路由處理、部署配置和測試工具。
