<?php

namespace App\Controllers;

use App\Models\Image;
use App\Services\AuthService;

/**
 * 圖片控制器
 * 處理圖片上傳、管理等功能
 */
class ImageController
{
    private $imageModel;
    
    public function __construct()
    {
        $this->imageModel = new Image();
    }

    /**
     * 上傳圖片
     */
    public function upload()
    {
        try {
            // 獲取當前用戶
            $currentUser = AuthService::getCurrentUser();
            if (!$currentUser) {
                http_response_code(401);
                echo json_encode([
                    'success' => false,
                    'error' => 'Authentication required'
                ]);
                return;
            }

            // 檢查是否有文件上傳
            if (!isset($_FILES['image'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => 'No image file provided'
                ]);
                return;
            }

            $file = $_FILES['image'];
            
            // 驗證圖片文件
            $validationErrors = Image::validateImage($file);
            if (!empty($validationErrors)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => implode(', ', $validationErrors)
                ]);
                return;
            }

            // 創建上傳目錄
            $uploadDir = __DIR__ . '/../../public/uploads/images/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // 生成安全的文件名
            $originalFilename = $file['name'];
            $safeFilename = Image::generateSafeFilename($originalFilename);
            $filePath = $uploadDir . $safeFilename;

            // 移動上傳的文件
            if (!move_uploaded_file($file['tmp_name'], $filePath)) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'error' => 'Failed to save uploaded file'
                ]);
                return;
            }

            // 獲取圖片信息
            $imageInfo = Image::getImageInfo($filePath);
            if (!$imageInfo) {
                // 如果無法獲取圖片信息，刪除文件
                unlink($filePath);
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => 'Invalid image file'
                ]);
                return;
            }

            // 構建文件 URL
            $baseUrl = $this->getBaseUrl();
            $fileUrl = $baseUrl . '/uploads/images/' . $safeFilename;

            // 獲取可選的日記 ID
            $entryId = $_POST['entry_id'] ?? null;
            if ($entryId && !is_numeric($entryId)) {
                $entryId = null;
            }

            // 保存圖片記錄到數據庫
            $imageData = [
                'filename' => $originalFilename,
                'stored_filename' => $safeFilename,
                'file_path' => $filePath,
                'file_url' => $fileUrl,
                'file_size' => $file['size'],
                'mime_type' => $imageInfo['mime_type'],
                'width' => $imageInfo['width'],
                'height' => $imageInfo['height'],
                'entry_id' => $entryId,
                'user_id' => $currentUser['id']
            ];

            $image = $this->imageModel->create($imageData);

            echo json_encode([
                'success' => true,
                'message' => 'Image uploaded successfully',
                'image' => $image
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Internal server error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 獲取用戶的圖片列表
     */
    public function getUserImages()
    {
        try {
            // 獲取當前用戶
            $currentUser = AuthService::getCurrentUser();
            if (!$currentUser) {
                http_response_code(401);
                echo json_encode([
                    'success' => false,
                    'error' => 'Authentication required'
                ]);
                return;
            }

            // 獲取分頁參數
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = min(100, max(1, intval($_GET['limit'] ?? 20)));
            $offset = ($page - 1) * $limit;

            // 獲取圖片列表
            $images = $this->imageModel->getByUserId($currentUser['id'], $limit, $offset);

            echo json_encode([
                'success' => true,
                'images' => $images,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => count($images)
                ]
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Internal server error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 刪除圖片
     */
    public function delete($imageId)
    {
        try {
            // 獲取當前用戶
            $currentUser = AuthService::getCurrentUser();
            if (!$currentUser) {
                http_response_code(401);
                echo json_encode([
                    'success' => false,
                    'error' => 'Authentication required'
                ]);
                return;
            }

            // 檢查圖片是否存在且屬於當前用戶
            $image = $this->imageModel->findById($imageId);
            if (!$image) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'error' => 'Image not found'
                ]);
                return;
            }

            if ($image['user_id'] != $currentUser['id']) {
                http_response_code(403);
                echo json_encode([
                    'success' => false,
                    'error' => 'Access denied'
                ]);
                return;
            }

            // 刪除圖片
            $result = $this->imageModel->delete($imageId);
            
            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Image deleted successfully'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'error' => 'Failed to delete image'
                ]);
            }

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Internal server error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 獲取圖片統計信息
     */
    public function getStats()
    {
        try {
            // 獲取當前用戶
            $currentUser = AuthService::getCurrentUser();
            if (!$currentUser) {
                http_response_code(401);
                echo json_encode([
                    'success' => false,
                    'error' => 'Authentication required'
                ]);
                return;
            }

            $stats = $this->imageModel->getStats($currentUser['id']);

            echo json_encode([
                'success' => true,
                'stats' => $stats
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Internal server error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 獲取基礎 URL
     */
    private function getBaseUrl()
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return $protocol . '://' . $host;
    }
}
