# 混合架構 - 前端整合指南

## 概述

本指南說明如何將React前端應用與混合架構後端（PHP API + Firebase FCM）進行整合。

## 技術架構

### 前端技術棧
- **React 19.1.0**: 前端框架
- **Material-UI 7.1.0**: UI組件庫
- **Axios**: HTTP客戶端
- **React Query**: 數據狀態管理
- **Firebase SDK**: 僅用於FCM推播
- **React Hook Form**: 表單處理

### 新增依賴
```bash
# 核心依賴
npm install axios
npm install @tanstack/react-query
npm install react-hot-toast

# Firebase（僅FCM）
npm install firebase

# 表單和驗證
npm install react-hook-form
npm install @hookform/resolvers yup

# 工具庫
npm install js-cookie
npm install date-fns
```

## Firebase FCM 配置

### 1. Firebase配置（僅FCM）
```javascript
// src/config/firebase.js
import { initializeApp } from 'firebase/app';
import { getMessaging, getToken, onMessage } from 'firebase/messaging';

const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.REACT_APP_FIREBASE_APP_ID
};

// 初始化Firebase（僅用於FCM）
const app = initializeApp(firebaseConfig);
export const messaging = getMessaging(app);

// VAPID密鑰
const vapidKey = process.env.REACT_APP_FIREBASE_VAPID_KEY;

// 獲取FCM Token
export const getFCMToken = async () => {
  try {
    const token = await getToken(messaging, { vapidKey });
    return token;
  } catch (error) {
    console.error('獲取FCM token失敗:', error);
    return null;
  }
};

// 監聽前台消息
export const onMessageListener = () =>
  new Promise((resolve) => {
    onMessage(messaging, (payload) => {
      resolve(payload);
    });
  });
```

### 2. Service Worker配置
```javascript
// public/firebase-messaging-sw.js
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id"
};

firebase.initializeApp(firebaseConfig);
const messaging = firebase.messaging();

// 處理背景消息
messaging.onBackgroundMessage((payload) => {
  console.log('收到背景消息:', payload);
  
  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    data: payload.data
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});

// 處理通知點擊
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  const data = event.notification.data;
  let url = '/';
  
  // 根據通知類型決定跳轉URL
  if (data.type === 'like' || data.type === 'comment') {
    url = `/app/entry/${data.entry_id}`;
  } else if (data.type === 'focus_reminder') {
    url = '/app/focus';
  }
  
  event.waitUntil(
    clients.openWindow(url)
  );
});
```

## API客戶端配置

### 1. Axios配置
```javascript
// src/services/api.js
import axios from 'axios';
import toast from 'react-hot-toast';
import Cookies from 'js-cookie';

// 創建axios實例
export const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost/gratitude-diary-api/public/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 請求攔截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加認證token
    const token = Cookies.get('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // 添加請求時間戳
    config.metadata = { startTime: new Date() };
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 響應攔截器
apiClient.interceptors.response.use(
  (response) => {
    // 計算請求時間
    const endTime = new Date();
    const duration = endTime - response.config.metadata.startTime;
    console.log(`API請求耗時: ${duration}ms`);
    
    return response.data;
  },
  (error) => {
    const { response } = error;
    
    if (response) {
      const { status, data } = response;
      
      // 處理不同的錯誤狀態
      switch (status) {
        case 401:
          // 未授權，清除token並重定向到登入頁
          Cookies.remove('auth_token');
          window.location.href = '/login';
          toast.error('登入已過期，請重新登入');
          break;
        case 403:
          toast.error('權限不足');
          break;
        case 404:
          toast.error('請求的資源不存在');
          break;
        case 422:
          // 驗證錯誤
          const message = data.error?.message || '數據驗證失敗';
          toast.error(message);
          break;
        case 500:
          toast.error('服務器內部錯誤');
          break;
        default:
          const errorMessage = data.error?.message || '請求失敗';
          toast.error(errorMessage);
      }
    } else {
      // 網路錯誤
      toast.error('網路連接失敗，請檢查網路設置');
    }
    
    return Promise.reject(error);
  }
);

// API響應格式化
export const formatApiResponse = (response) => {
  if (response.success) {
    return {
      success: true,
      data: response.data,
      message: response.message
    };
  } else {
    throw new Error(response.error?.message || '請求失敗');
  }
};
```

### 2. 認證服務
```javascript
// src/services/authService.js
import { apiClient } from './api';
import { getFCMToken } from '../config/firebase';
import Cookies from 'js-cookie';

export const authService = {
  // 用戶註冊
  register: async (userData) => {
    try {
      // 獲取FCM token
      const fcmToken = await getFCMToken();
      
      const response = await apiClient.post('/auth/register', {
        ...userData,
        fcm_token: fcmToken,
        platform: 'web'
      });
      
      if (response.success) {
        // 保存token
        Cookies.set('auth_token', response.data.token, { expires: 7 });
        return response.data;
      }
      
      throw new Error(response.error?.message || '註冊失敗');
    } catch (error) {
      throw error;
    }
  },

  // 用戶登入
  login: async (email, password) => {
    try {
      // 獲取FCM token
      const fcmToken = await getFCMToken();
      
      const response = await apiClient.post('/auth/login', {
        email,
        password,
        fcm_token: fcmToken,
        platform: 'web'
      });
      
      if (response.success) {
        // 保存token
        Cookies.set('auth_token', response.data.token, { expires: 7 });
        return response.data;
      }
      
      throw new Error(response.error?.message || '登入失敗');
    } catch (error) {
      throw error;
    }
  },

  // 用戶登出
  logout: async () => {
    try {
      const fcmToken = await getFCMToken();
      
      await apiClient.post('/auth/logout', {
        fcm_token: fcmToken
      });
      
      // 清除本地token
      Cookies.remove('auth_token');
      
      return true;
    } catch (error) {
      // 即使API調用失敗，也要清除本地token
      Cookies.remove('auth_token');
      throw error;
    }
  },

  // 檢查登入狀態
  isAuthenticated: () => {
    return !!Cookies.get('auth_token');
  },

  // 獲取當前token
  getToken: () => {
    return Cookies.get('auth_token');
  }
};
```

### 3. 日記服務
```javascript
// src/services/entryService.js
import { apiClient } from './api';

export const entryService = {
  // 獲取日記列表
  getEntries: async (params = {}) => {
    const response = await apiClient.get('/entries', { params });
    return response.data;
  },

  // 獲取單個日記
  getEntry: async (entryId) => {
    const response = await apiClient.get(`/entries/${entryId}`);
    return response.data;
  },

  // 創建日記
  createEntry: async (entryData) => {
    const response = await apiClient.post('/entries', entryData);
    return response.data;
  },

  // 更新日記
  updateEntry: async (entryId, entryData) => {
    const response = await apiClient.put(`/entries/${entryId}`, entryData);
    return response.data;
  },

  // 刪除日記
  deleteEntry: async (entryId) => {
    const response = await apiClient.delete(`/entries/${entryId}`);
    return response.data;
  },

  // 點讚日記
  likeEntry: async (entryId) => {
    const response = await apiClient.post(`/entries/${entryId}/like`);
    return response.data;
  },

  // 獲取評論
  getComments: async (entryId, params = {}) => {
    const response = await apiClient.get(`/entries/${entryId}/comments`, { params });
    return response.data;
  },

  // 添加評論
  addComment: async (entryId, content, parentId = null) => {
    const response = await apiClient.post(`/entries/${entryId}/comments`, {
      content,
      parent_id: parentId
    });
    return response.data;
  },

  // 上傳媒體文件
  uploadMedia: async (file, onProgress) => {
    const formData = new FormData();
    formData.append('media', file);

    const response = await apiClient.post('/media/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          onProgress(percentCompleted);
        }
      },
    });

    return response.data;
  }
};
```

## React Query整合

### 1. Query Client配置
```javascript
// src/config/queryClient.js
import { QueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error) => {
        // 對於認證錯誤不重試
        if (error?.response?.status === 401) {
          return false;
        }
        return failureCount < 2;
      },
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5分鐘
      cacheTime: 10 * 60 * 1000, // 10分鐘
    },
    mutations: {
      retry: 1,
      onError: (error) => {
        // 全局錯誤處理
        console.error('Mutation error:', error);
      },
    },
  },
});
```

### 2. 自定義Hooks
```javascript
// src/hooks/useAuth.js
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { authService } from '../services/authService';
import toast from 'react-hot-toast';

export const useAuth = () => {
  const queryClient = useQueryClient();

  // 登入
  const loginMutation = useMutation({
    mutationFn: ({ email, password }) => authService.login(email, password),
    onSuccess: (data) => {
      queryClient.setQueryData(['user'], data.user);
      toast.success('登入成功！');
    },
    onError: (error) => {
      toast.error(error.message || '登入失敗');
    },
  });

  // 註冊
  const registerMutation = useMutation({
    mutationFn: (userData) => authService.register(userData),
    onSuccess: (data) => {
      queryClient.setQueryData(['user'], data.user);
      toast.success('註冊成功！');
    },
    onError: (error) => {
      toast.error(error.message || '註冊失敗');
    },
  });

  // 登出
  const logoutMutation = useMutation({
    mutationFn: () => authService.logout(),
    onSuccess: () => {
      queryClient.clear();
      toast.success('已安全登出');
    },
    onError: (error) => {
      console.error('登出錯誤:', error);
      // 即使出錯也清除本地數據
      queryClient.clear();
    },
  });

  return {
    login: loginMutation.mutate,
    register: registerMutation.mutate,
    logout: logoutMutation.mutate,
    isLoggingIn: loginMutation.isPending,
    isRegistering: registerMutation.isPending,
    isLoggingOut: logoutMutation.isPending,
    isAuthenticated: authService.isAuthenticated(),
  };
};
```

```javascript
// src/hooks/useEntries.js
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { entryService } from '../services/entryService';
import toast from 'react-hot-toast';

export const useEntries = (params = {}) => {
  return useQuery({
    queryKey: ['entries', params],
    queryFn: () => entryService.getEntries(params),
    enabled: !!authService.isAuthenticated(),
  });
};

export const useEntry = (entryId) => {
  return useQuery({
    queryKey: ['entry', entryId],
    queryFn: () => entryService.getEntry(entryId),
    enabled: !!entryId && !!authService.isAuthenticated(),
  });
};

export const useCreateEntry = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: entryService.createEntry,
    onSuccess: (data) => {
      // 更新日記列表緩存
      queryClient.invalidateQueries({ queryKey: ['entries'] });
      toast.success('日記創建成功！');
    },
    onError: (error) => {
      toast.error(error.message || '創建日記失敗');
    },
  });
};

export const useLikeEntry = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: entryService.likeEntry,
    onSuccess: (data, entryId) => {
      // 更新相關緩存
      queryClient.invalidateQueries({ queryKey: ['entries'] });
      queryClient.invalidateQueries({ queryKey: ['entry', entryId] });
    },
    onError: (error) => {
      toast.error(error.message || '操作失敗');
    },
  });
};

export const useUploadMedia = () => {
  return useMutation({
    mutationFn: ({ file, onProgress }) => entryService.uploadMedia(file, onProgress),
    onError: (error) => {
      toast.error(error.message || '文件上傳失敗');
    },
  });
};
```

## 推播通知整合

### 3. 通知服務
```javascript
// src/services/notificationService.js
import { messaging, onMessageListener } from '../config/firebase';
import toast from 'react-hot-toast';

class NotificationService {
  constructor() {
    this.isInitialized = false;
  }

  async initialize() {
    if (this.isInitialized) return;

    try {
      // 請求通知權限
      const permission = await Notification.requestPermission();
      
      if (permission === 'granted') {
        console.log('通知權限已授予');
        
        // 監聽前台消息
        this.listenForMessages();
        
        this.isInitialized = true;
      } else {
        console.log('通知權限被拒絕');
      }
    } catch (error) {
      console.error('初始化通知服務失敗:', error);
    }
  }

  listenForMessages() {
    onMessageListener()
      .then((payload) => {
        console.log('收到前台消息:', payload);
        
        const { notification, data } = payload;
        
        // 顯示toast通知
        toast.custom((t) => (
          <div
            className={`${
              t.visible ? 'animate-enter' : 'animate-leave'
            } max-w-md w-full bg-white shadow-lg rounded-lg pointer-events-auto flex ring-1 ring-black ring-opacity-5`}
          >
            <div className="flex-1 w-0 p-4">
              <div className="flex items-start">
                <div className="ml-3 flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    {notification.title}
                  </p>
                  <p className="mt-1 text-sm text-gray-500">
                    {notification.body}
                  </p>
                </div>
              </div>
            </div>
            <div className="flex border-l border-gray-200">
              <button
                onClick={() => {
                  toast.dismiss(t.id);
                  this.handleNotificationClick(data);
                }}
                className="w-full border border-transparent rounded-none rounded-r-lg p-4 flex items-center justify-center text-sm font-medium text-indigo-600 hover:text-indigo-500 focus:outline-none"
              >
                查看
              </button>
            </div>
          </div>
        ), {
          duration: 5000,
        });
      })
      .catch((error) => {
        console.error('監聽消息失敗:', error);
      });
  }

  handleNotificationClick(data) {
    // 根據通知類型處理點擊事件
    if (data.type === 'like' || data.type === 'comment') {
      window.location.href = `/app/entry/${data.entry_id}`;
    } else if (data.type === 'focus_reminder') {
      window.location.href = '/app/focus';
    } else {
      window.location.href = '/app';
    }
  }

  async getToken() {
    try {
      const token = await getFCMToken();
      return token;
    } catch (error) {
      console.error('獲取FCM token失敗:', error);
      return null;
    }
  }
}

export const notificationService = new NotificationService();
```

## 應用主入口更新

### 4. App.jsx更新
```javascript
// src/App.jsx
import React, { useEffect } from 'react';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Toaster } from 'react-hot-toast';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

import { queryClient } from './config/queryClient';
import { notificationService } from './services/notificationService';
import theme from './theme';

// 頁面組件
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import CreateDiaryPage from './pages/CreateDiaryPage';
import ProfilePage from './pages/ProfilePage';
import FocusPage from './pages/FocusPage';
import ProtectedRoute from './components/ProtectedRoute';

function App() {
  useEffect(() => {
    // 初始化推播通知服務
    notificationService.initialize();
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <Routes>
            {/* 公開路由 */}
            <Route path="/login" element={<LoginPage />} />
            <Route path="/register" element={<RegisterPage />} />

            {/* 受保護的路由 */}
            <Route path="/" element={
              <ProtectedRoute>
                <HomePage />
              </ProtectedRoute>
            } />
            <Route path="/app" element={
              <ProtectedRoute>
                <HomePage />
              </ProtectedRoute>
            } />
            <Route path="/app/create" element={
              <ProtectedRoute>
                <CreateDiaryPage />
              </ProtectedRoute>
            } />
            <Route path="/app/profile" element={
              <ProtectedRoute>
                <ProfilePage />
              </ProtectedRoute>
            } />
            <Route path="/app/focus" element={
              <ProtectedRoute>
                <FocusPage />
              </ProtectedRoute>
            } />
          </Routes>
        </Router>

        {/* Toast通知 */}
        <Toaster
          position="top-center"
          toastOptions={{
            duration: 3000,
            style: {
              background: '#363636',
              color: '#fff',
            },
            success: {
              style: {
                background: '#10B981',
              },
            },
            error: {
              style: {
                background: '#EF4444',
              },
            },
          }}
        />
      </ThemeProvider>

      {/* React Query開發工具 */}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}

export default App;
```

### 5. 路由保護組件
```javascript
// src/components/ProtectedRoute.jsx
import React from 'react';
import { Navigate } from 'react-router-dom';
import { authService } from '../services/authService';

const ProtectedRoute = ({ children }) => {
  const isAuthenticated = authService.isAuthenticated();

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return children;
};

export default ProtectedRoute;
```

## 頁面組件更新

### 6. 登入頁面
```javascript
// src/pages/LoginPage.jsx
import React from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { Link, useNavigate } from 'react-router-dom';
import {
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Box,
  CircularProgress
} from '@mui/material';

import { useAuth } from '../hooks/useAuth';

const schema = yup.object({
  email: yup.string().email('請輸入有效的電子郵件').required('電子郵件為必填'),
  password: yup.string().min(6, '密碼至少需要6個字符').required('密碼為必填'),
});

const LoginPage = () => {
  const navigate = useNavigate();
  const { login, isLoggingIn } = useAuth();

  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(schema)
  });

  const onSubmit = async (data) => {
    try {
      await login(data);
      navigate('/app');
    } catch (error) {
      // 錯誤已在useAuth中處理
    }
  };

  return (
    <Container component="main" maxWidth="xs">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Paper elevation={3} sx={{ padding: 4, width: '100%' }}>
          <Typography component="h1" variant="h4" align="center" gutterBottom>
            感恩日記
          </Typography>
          <Typography variant="h6" align="center" color="text.secondary" gutterBottom>
            登入您的帳戶
          </Typography>

          <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="email"
              label="電子郵件"
              name="email"
              autoComplete="email"
              autoFocus
              {...register('email')}
              error={!!errors.email}
              helperText={errors.email?.message}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="密碼"
              type="password"
              id="password"
              autoComplete="current-password"
              {...register('password')}
              error={!!errors.password}
              helperText={errors.password?.message}
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={isLoggingIn}
            >
              {isLoggingIn ? <CircularProgress size={24} /> : '登入'}
            </Button>
            <Box textAlign="center">
              <Link to="/register">
                還沒有帳戶？立即註冊
              </Link>
            </Box>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default LoginPage;
```

### 7. 首頁更新
```javascript
// src/pages/HomePage.jsx (部分更新)
import React, { useState } from 'react';
import { useEntries, useLikeEntry } from '../hooks/useEntries';
import LoadingSpinner from '../components/LoadingSpinner';

const HomePage = () => {
  const [selectedEmotion, setSelectedEmotion] = useState('all');
  const [page, setPage] = useState(1);

  const {
    data: entriesData,
    isLoading,
    error
  } = useEntries({
    emotion: selectedEmotion !== 'all' ? selectedEmotion : undefined,
    page,
    limit: 20
  });

  const likeMutation = useLikeEntry();

  const handleLike = (entryId) => {
    likeMutation.mutate(entryId);
  };

  if (isLoading) {
    return <LoadingSpinner message="載入日記中..." />;
  }

  if (error) {
    return (
      <Box sx={{ textAlign: 'center', mt: 4 }}>
        <Typography variant="h6" color="error">
          載入失敗：{error.message}
        </Typography>
      </Box>
    );
  }

  const entries = entriesData?.entries || [];

  // 其餘組件邏輯保持不變，但使用真實數據
  return (
    <div>
      {/* 現有的JSX結構，但使用entries數據 */}
    </div>
  );
};

export default HomePage;
```

## 環境配置

### 8. 環境變數
```bash
# .env.local
# API配置
REACT_APP_API_BASE_URL=http://localhost/gratitude-diary-api/public/api/v1

# Firebase配置（僅FCM）
REACT_APP_FIREBASE_API_KEY=your-firebase-api-key
REACT_APP_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your-project-id
REACT_APP_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
REACT_APP_FIREBASE_APP_ID=your-app-id
REACT_APP_FIREBASE_VAPID_KEY=your-vapid-key

# 應用配置
REACT_APP_ENV=development
REACT_APP_VERSION=1.0.0
```

### 9. 建置腳本更新
```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "build:staging": "REACT_APP_ENV=staging vite build",
    "build:production": "REACT_APP_ENV=production vite build",
    "preview": "vite preview",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "lint": "eslint src --ext .js,.jsx,.ts,.tsx",
    "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix"
  }
}
```

## 錯誤處理和調試

### 10. 全局錯誤邊界
```javascript
// src/components/ErrorBoundary.jsx
import React from 'react';
import { Box, Typography, Button } from '@mui/material';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('錯誤邊界捕獲錯誤:', error, errorInfo);

    // 可以在這裡發送錯誤報告到監控服務
    if (process.env.NODE_ENV === 'production') {
      // 發送錯誤報告
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <Box sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          p: 3
        }}>
          <Typography variant="h5" sx={{ mb: 2 }}>
            哎呀，出現了一些問題
          </Typography>
          <Typography variant="body1" sx={{ mb: 3, textAlign: 'center' }}>
            我們正在努力修復這個問題，請稍後再試。
          </Typography>
          <Button
            variant="contained"
            onClick={() => window.location.reload()}
          >
            重新載入
          </Button>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
```

這個混合架構前端整合指南提供了完整的React應用與PHP API + Firebase FCM的整合方案，包括認證、數據管理、推播通知、錯誤處理等各個方面。
